FROM node:20-alpine3.20

ARG PROJECT
ENV PROJECT_NAME=$PROJECT \
    PNPM_HOME=/root/.pnpm-store \
    NPM_CONFIG_REGISTRY=https://registry.npmmirror.com

WORKDIR /app

RUN corepack enable && corepack prepare pnpm@8.6.6 --activate

#COPY pnpm-workspace.yaml package.json pnpm-lock.yaml ./

COPY . .

RUN pnpm install

RUN pnpm run prisma_generate

# 根据项目设置工作目录并启动
CMD cd apps/$PROJECT_NAME && pnpm run client