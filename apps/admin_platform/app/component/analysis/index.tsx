'use client'
import dayjs from 'dayjs'
import { useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { TiTick } from 'react-icons/ti'
import { RxCross2 } from 'react-icons/rx'
import {
  MaterialReactTable,
  useMaterialReactTable,
  type MRT_ColumnDef
} from 'material-react-table'
import { AnalysisData } from '@/app/type/analysis'
import Link from 'next/link'
import { updateAttendAndCompleteCourseData } from '@/app/yuhe/api/analysis'
import { download } from '@/app/lib/download'
import { CompleteStatusPerDay } from './complete_status_per_day'

export function Analysis({ getAnalysisData, chatHistoryPageLinkPrefix }:{getAnalysisData:(startCourseNo:number, endCourseNo:number)=>Promise<AnalysisData[]>, chatHistoryPageLinkPrefix:string }) {
  const [startCourseNo, setStartCourseNo] = useState<number>(Number(dayjs().subtract(7, 'day').format('YYYYMMDD')))
  const [columnFilters, setColumnFilters] = useState<ColumnFilter[]>([])
  const [endCourseNo, setEndCourseNo] = useState<number>(Number(dayjs().format('YYYYMMDD')))
  const [loading, setLoading] = useState<boolean>(true)
  const [data, setData] = useState<AnalysisData[]>([])
  const [filterData, setFilterData] = useState<AnalysisData[]>([])
  const [startCourseNoShow, setStartCourseNoShow] = useState<number>(0)
  const [endCourseNoShow, setEndCourseNoShow] = useState<number>(0)
  useEffect(() => {
    query(startCourseNo, endCourseNo)
  }, [])
  useEffect(() => {
    setFilterData(table.getFilteredRowModel().rows.map((item) => item.original))
  }, [columnFilters])
  const dates = useMemo(() => {
    const dateList:number[] = []
    for (let now = startCourseNoShow; now <= endCourseNoShow; now = Number(dayjs(String(now), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD'))) {
      dateList.push(now)
    }
    return [...dateList]
  }, [startCourseNoShow, endCourseNoShow])
  const assistant = useMemo(() => {
    return [...new Set<string>(...[filterData.map((item) => `${item.assistant}-${item.assistantName}`)])]
  }, [filterData])
  const columns = useMemo<MRT_ColumnDef<AnalysisData>[]>(() => [
    {
      accessorKey: 'chatId',
      header:'chat_id',
      size:150,
    },
    {
      accessorKey: 'name',
      header:'昵称',
      size:150,
      enableHiding:true
    },
    {
      accessorKey:'courseNo',
      header: '期数',
      filterVariant:'range',
      filterFn:'betweenInclusive',
      size:150
    },
    {
      accessorKey:'courseNoOri',
      header: '原期数',
      size:150,
      filterVariant:'range',
      filterFn:'betweenInclusive',
      Cell: ({ cell }) => cell.getValue<number>() == 0 ? '' : cell.getValue<number>()
    },
    {
      accessorKey:'isDelay',
      header: '是否延期',
      size:150,
      filterVariant:'select',
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />,
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
    },
    {
      accessorKey:'assistantName',
      header:'助教名称',
      size:150
    },
    {
      accessorKey:'assistant',
      header:'助教账号',
      size:150
    },
    {
      accessorKey:'phone',
      header:'电话',
      size:150
    },
    {
      accessorKey:'ip',
      header:'ip',
      size:150
    },
    {
      accessorKey:'chatNumber',
      header: '客户发言次数',
      size:150
    },
    {
      accessorKey:'isFillAnyUserSlots',
      header: '填写了一点点客户画像',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isFillUserSlots',
      header: '填写了客户画像',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteDouyinAnalysis',
      header: '抖音截图分析',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework1',
      header: '第一节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework2',
      header: '第二节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteHomework3',
      header: '第三节课作业',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay1',
      header: '第一节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay2',
      header: '第二节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay3',
      header: '第三节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isAttendCourseDay4',
      header: '第四节课到课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay1',
      header: '第一节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay2',
      header: '第二节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay3',
      header: '第三节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isCompleteCourseDay4',
      header: '第四节课完课',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isInviteGroupFailAfterPayment',
      header: '下单后是否拉群成功',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'isPaid',
      header: '下单',
      size:50,
      filterVariant:'select',
      filterSelectOptions:[{ label:'是', value:'true' }, { label:'否', value:'false' }],
      Cell: ({ cell }) => cell.getValue<boolean>() ? <TiTick /> : <RxCross2 />
    },
    {
      accessorKey:'paidTime',
      header: '下单时间',
      size:50,
      Cell: ({ cell }) => {
        const value = cell.getValue<Date | null>()
        if (!value) {
          return <div></div>
        }
        return <div>{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</div>
      }
    },
    {
      accessorKey:'paymentNumber',
      header: '下单数',
      size:150
    },
  ], [])
  const table = useMaterialReactTable({
    columns,
    data, //data must be memoized or stable (useState, useMemo, defined outside of this component, etc.)
    state:{
      columnFilters
    },
    onColumnFiltersChange: setColumnFilters,
    enableColumnPinning: true,
    enableRowActions: true,
    renderRowActions: ({ row }) => (
      <Link href={`${chatHistoryPageLinkPrefix}/${row.getValue('chatId')}`}><button className='btn btn-info btn-soft btn-sm m-2'>jump</button></Link>
    ),
    initialState:{
      pagination:{ pageSize:50, pageIndex:0 }, density:'compact',
      columnPinning:{
        left:['name', 'courseNo', 'assistantName']
      },
      columnVisibility:{
        chatId:false,
      },
      sorting: [
        {
          id: 'chatNumber', //sort by age by default on page load
          desc: true,
        },
      ],
    }
  })
  const query = (startCourseNo:number, endCourseNo:number) => {
    setLoading(true)
    toast.promise(getAnalysisData(startCourseNo, endCourseNo), {
      pending:'query pending',
      success:'query success',
      error: 'query error'
    }).then((result) => {
      setData(result)
      setFilterData(result)
      setStartCourseNoShow(startCourseNo)
      setEndCourseNoShow(endCourseNo)
    }).finally(() => {
      setLoading(false)
    })
  }
  const exportData = () => {
    let text = '昵称,手机号,期数,原期数,助教账号,助教名称,ip,客户发言次数,填写了一点点客户画像,填写了客户画像,抖音截图分析,第一节课作业,第二节课作业,第三节课作业,第一节课到课,第二节课到课,第三节课到课,第四节课到课,第一节课完课,第二节课完课,第三节课完课,第四节课完课,下单,下单时间,下单数\n'
    for (const user of filterData) {
      text += `${user.name},${user.phone},${user.courseNo},${user.courseNoOri},${user.assistant},${user.assistantName},${user.ip},${user.chatNumber},${user.isFillAnyUserSlots},${user.isFillUserSlots},${user.isCompleteDouyinAnalysis},${user.isCompleteHomework1},${user.isCompleteHomework2},${user.isCompleteHomework3},${user.isAttendCourseDay1},${user.isAttendCourseDay2},${user.isAttendCourseDay3},${user.isAttendCourseDay4},${user.isCompleteCourseDay1},${user.isCompleteCourseDay2},${user.isCompleteCourseDay3},${user.isCompleteCourseDay4},${user.isPaid},${dayjs(user.paidTime).format('YYYY-MM-DD HH:mm:ss')},${user.paymentNumber}\n`
    }
    download(text)
  }



  const exportEntryStatistics = () => {
    let text = `助教名称,${dates.join(',')}\n`
    for (const singleAssistant of assistant) {
      text += `${singleAssistant},${dates.map((date) => filterData.filter((detail) => detail.courseNoOri == date && detail.assistant == singleAssistant.split('-')[0]).length).join(',')}\n`
    }
    text += `总量,${dates.map((date) => filterData.filter((detail) => detail.courseNoOri == date).length).join(',')}`
    download(text, 'analysis_entry_statistic.csv')
  }
  if (loading) {
    return <>loading</>
  }
  return <div className='m-2'>
    <h2 className='text-3xl p-2 font-semibold'>analysis</h2>
    <div className='flex gap-2 p-1'>
      <label className='label w-30'>start course no:</label>
      <input type="number" className='input' value={startCourseNo} onChange={(e) => { setStartCourseNo(Number(e.currentTarget.value)) }}/>
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={exportData}>export</button>
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={exportEntryStatistics}>export entry data</button>
    </div>
    <div className='flex gap-2 p-1'>
      <label className='label w-30'>end course no:</label>
      <input type="number" className='input' value={endCourseNo} onChange={(e) => { setEndCourseNo(Number(e.currentTarget.value)) }} />
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={() => {
        query(startCourseNo, endCourseNo)
      }}>query</button>
      <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={(e) => {
        e.preventDefault()
        setLoading(true)
        toast.promise(updateAttendAndCompleteCourseData(), {
          pending: 'update pending',
          success: 'update success',
          error: 'update error'
        }).finally(() => {
          setLoading(false)
        })
      }}>update attend and complete status</button>
    </div>
    <div className='flex gap-x-12 gap-y-4 py-4 justify-start flex-wrap'>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>下单率</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isPaid).map((item) => item.paymentNumber).reduce((acc, n) => acc + n, 0) / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isPaid).map((item) => item.paymentNumber).reduce((acc, n) => acc + n, 0)} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>平均对话轮数</div>
          <div className='stat-value'>{(filterData.map((item) => item.chatNumber).reduce((pre, cur) => pre + cur, 0) / filterData.length).toFixed(2)}</div>
          <div className='stat-desc'>{filterData.map((item) => item.chatNumber).reduce((pre, cur) => pre + cur, 0)} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>填写了一点点客户画像</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isFillAnyUserSlots).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isFillAnyUserSlots).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>填写了客户画像</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isFillUserSlots).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isFillUserSlots).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='shadow stats'>
        <div className='stat'>
          <div className='stat-title'>抖音截图诊断</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteDouyinAnalysis).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteDouyinAnalysis).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第一课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三课作业</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteHomework3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteHomework3).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='stats shadow'>
        <div className='stat'>
          <div className='stat-title'>第一节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay3).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第四节课到课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isAttendCourseDay4).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isAttendCourseDay4).length} / {filterData.length}</div>
        </div>
      </div>
      <div className='stats shadow'>
        <div className='stat'>
          <div className='stat-title'>第一节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay1).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay1).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第二节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay2).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay2).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第三节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay3).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay3).length} / {filterData.length}</div>
        </div>
        <div className='stat'>
          <div className='stat-title'>第四节课完课</div>
          <div className='stat-value'>{(filterData.filter((item) => item.isCompleteCourseDay4).length / filterData.length * 100).toFixed(2)}%</div>
          <div className='stat-desc'>{filterData.filter((item) => item.isCompleteCourseDay4).length} / {filterData.length}</div>
        </div>
      </div>
    </div>
    <div className="tabs tabs-lift">
      <input type="radio" name="my_tabs_3" className="tab" aria-label="data analysis" defaultChecked />
      <div className="tab-content bg-base-100 border-base-300 p-6">
        <div className='flex justify-center'>
          <div className='max-w-full'>
            <MaterialReactTable table={table} />
          </div>
        </div>
      </div>

      <input type="radio" name="my_tabs_3" className="tab" aria-label="User entry statistics" />
      <div className="tab-content bg-base-100 border-base-300 p-6">
        <div className="overflow-x-auto max-w-[85dvw]">
          <table className="table">
            {/* head */}
            <thead>
              <tr>
                <th>助教名称</th>
                {dates.map((date) => <th key={ date }>{date}</th>)}
              </tr>
            </thead>
            <tbody>
              {assistant.map((item) => {
                return <tr key={item}>
                  <th>{item}</th>
                  {dates.map((date) => {
                    return <td key={date}>
                      {filterData.filter((detail) => detail.courseNoOri == date && detail.assistant == item.split('-')[0]).length}
                    </td>
                  })}
                </tr>
              })}
              <tr>
                <th>总量</th>
                {dates.map((date) => {
                  return <td key={date}>
                    {filterData.filter((detail) => detail.courseNoOri == date).length}
                  </td>
                })}
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <input type="radio" name="my_tabs_3" className="tab" aria-label="statistics" />
      <div className="tab-content bg-base-100 border-base-300 p-6">
        <Statistics data={filterData}/>
      </div>
    </div>
  </div>
}

function Statistics({ data }:{data:AnalysisData[]}) {
  const ips = [...new Set<string>(data.map((item) => item.ip))]
  const dates = [... new Set<number>(data.map((item) => item.courseNoOri))]
  const statisticsData:StatisticsData[] = []
  for (const ip of ips) {
    const ipAggregation:StatisticsData = {
      ip: ip,
      courseNo: 0,
      studentNumber: 0,
      delay: 0,
      paid: 0,
      paidDuringClassDay2: 0,
      paidNotDuringClassDay2: 0,
      paidDuringClassDay3: 0,
      paidNotDuringClassDay3: 0,
      paidDuringClassDay4: 0,
      paidNotDuringClassDay4: 0,
      fillAnyUserSlots: 0,
      fillUserSlots: 0,
      douyinScreenShotAnalysis: 0,
      isAttendCourseDay1: 0,
      isAttendCourseDay2: 0,
      isAttendCourseDay3: 0,
      isAttendCourseDay4: 0,
      isCompleteCourseDay1: 0,
      isCompleteCourseDay2: 0,
      isCompleteCourseDay3: 0,
      isCompleteCourseDay4: 0,
      isCompleteHomeworkDay1: 0,
      isCompleteHomeworkDay2: 0,
      isCompleteHomeworkDay3: 0
    }
    for (const date of dates) {
      const filtedData = data.filter((item) => item.ip == ip && item.courseNoOri == date)
      if (filtedData.length == 0) continue
      const willPushData:StatisticsData = {
        ip: ip,
        courseNo: date,
        studentNumber: filtedData.length,
        delay: filtedData.filter((item) => item.isDelay).length,
        paid: filtedData.filter((item) => item.isPaid).length,
        fillAnyUserSlots: filtedData.filter((item) => item.isFillAnyUserSlots).length,
        fillUserSlots: filtedData.filter((item) => item.isFillUserSlots).length,
        douyinScreenShotAnalysis: filtedData.filter((item) => item.isCompleteDouyinAnalysis).length,
        isAttendCourseDay1: filtedData.filter((item) => item.isAttendCourseDay1).length,
        isAttendCourseDay2: filtedData.filter((item) => item.isAttendCourseDay2).length,
        isAttendCourseDay3: filtedData.filter((item) => item.isAttendCourseDay3).length,
        isAttendCourseDay4: filtedData.filter((item) => item.isAttendCourseDay4).length,
        isCompleteCourseDay1: filtedData.filter((item) => item.isCompleteCourseDay1).length,
        isCompleteCourseDay2: filtedData.filter((item) => item.isCompleteCourseDay2).length,
        isCompleteCourseDay3: filtedData.filter((item) => item.isCompleteCourseDay3).length,
        isCompleteCourseDay4: filtedData.filter((item) => item.isCompleteCourseDay4).length,
        isCompleteHomeworkDay1: filtedData.filter((item) => item.isCompleteHomework1).length,
        isCompleteHomeworkDay2: filtedData.filter((item) => item.isCompleteHomework2).length,
        isCompleteHomeworkDay3: filtedData.filter((item) => item.isCompleteHomework3).length,
        paidDuringClassDay2: filtedData.filter((item) => item.isPaidDuringClassDay2).length,
        paidNotDuringClassDay2: filtedData.filter((item) => item.isPaidNotDuringClassDay2).length,
        paidDuringClassDay3: filtedData.filter((item) => item.isPaidDuringClassDay3).length,
        paidNotDuringClassDay3: filtedData.filter((item) => item.isPaidNotDuringClassDay3).length,
        paidDuringClassDay4: filtedData.filter((item) => item.isPaidDuringClassDay4).length,
        paidNotDuringClassDay4: filtedData.filter((item) => item.isPaidNotDuringClassDay4).length
      }
      statisticsData.push(willPushData)
      ipAggregation.studentNumber += willPushData.studentNumber
      ipAggregation.delay += willPushData.delay
      ipAggregation.paid += willPushData.paid
      ipAggregation.paidDuringClassDay2 += willPushData.paidDuringClassDay2
      ipAggregation.paidDuringClassDay3 += willPushData.paidDuringClassDay3
      ipAggregation.paidDuringClassDay4 += willPushData.paidDuringClassDay4
      ipAggregation.paidNotDuringClassDay2 += willPushData.paidNotDuringClassDay2
      ipAggregation.paidNotDuringClassDay3 += willPushData.paidNotDuringClassDay3
      ipAggregation.paidNotDuringClassDay4 += willPushData.paidNotDuringClassDay4
      ipAggregation.fillAnyUserSlots += willPushData.fillAnyUserSlots
      ipAggregation.fillUserSlots += willPushData.fillUserSlots
      ipAggregation.douyinScreenShotAnalysis += willPushData.douyinScreenShotAnalysis
      ipAggregation.isAttendCourseDay1 += willPushData.isAttendCourseDay1
      ipAggregation.isAttendCourseDay2 += willPushData.isAttendCourseDay2
      ipAggregation.isAttendCourseDay3 += willPushData.isAttendCourseDay3
      ipAggregation.isAttendCourseDay4 += willPushData.isAttendCourseDay4
      ipAggregation.isCompleteCourseDay1 += willPushData.isCompleteCourseDay1
      ipAggregation.isCompleteCourseDay2 += willPushData.isCompleteCourseDay2
      ipAggregation.isCompleteCourseDay3 += willPushData.isCompleteCourseDay3
      ipAggregation.isCompleteCourseDay4 += willPushData.isCompleteCourseDay4
      ipAggregation.isCompleteHomeworkDay1 += willPushData.isCompleteHomeworkDay1
      ipAggregation.isCompleteHomeworkDay2 += willPushData.isCompleteHomeworkDay2
      ipAggregation.isCompleteHomeworkDay3 += willPushData.isCompleteHomeworkDay3
    }
    statisticsData.push(ipAggregation)
  }
  const exportData = () => {
    let text = ['ip', '期数', '状态', '学员数', '延期', '成单数', '转化率', 'Day2课上', 'Day2课下', 'Day3课上', 'Day3课下', 'Day4课上', 'Day4课下', '填写部分客户画像', '填写了客户画像', '抖音截图分析', '第一节课到课', '第一节课完课', '第二节课到课', '第二节课完课', '第三节课到课', '第三节课完课', '第四节课到课', '第四节课完课', '第一节课作业', '第二节课作业', '第三节课作业'].join()
    text += '\n'
    for (const item of statisticsData) {
      text += `${item.ip},${item.courseNo == 0 ? '汇总' : item.courseNo},${dayjs().hour(0).minute(0).second(0).diff(dayjs(String(item.courseNo), 'YYYYMMDD'), 'day') > 4 ? '结课' : '行课'},${item.studentNumber},${item.delay} | ${(item.delay / item.studentNumber * 100).toFixed(2)}%,${item.paid},${(item.paid / item.studentNumber * 100).toFixed(2)}%,${item.paidDuringClassDay2} | ${(item.paidDuringClassDay2 / item.studentNumber * 100).toFixed(2)}%,${item.paidNotDuringClassDay2} | ${(item.paidNotDuringClassDay2 / item.studentNumber * 100).toFixed(2)}%,${item.paidDuringClassDay3} | ${(item.paidDuringClassDay3 / item.studentNumber * 100).toFixed(2)}%,${item.paidNotDuringClassDay3} | ${(item.paidNotDuringClassDay3 / item.studentNumber * 100).toFixed(2)}%,${item.paidDuringClassDay4} | ${(item.paidDuringClassDay4 / item.studentNumber * 100).toFixed(2)}%,${item.paidNotDuringClassDay4} | ${(item.paidNotDuringClassDay4 / item.studentNumber * 100).toFixed(2)}%,${(item.fillAnyUserSlots / item.studentNumber * 100).toFixed(2)}%,${(item.fillUserSlots / item.studentNumber * 100).toFixed(2)}%,${(item.douyinScreenShotAnalysis / item.studentNumber * 100).toFixed(2)}%,${(item.isAttendCourseDay1 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteCourseDay1 / item.studentNumber * 100).toFixed(2)}%,${(item.isAttendCourseDay2 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteCourseDay2 / item.studentNumber * 100).toFixed(2)}%,${(item.isAttendCourseDay3 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteCourseDay3 / item.studentNumber * 100).toFixed(2)}%,${(item.isAttendCourseDay4 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteCourseDay4 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteHomeworkDay1 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteHomeworkDay2 / item.studentNumber * 100).toFixed(2)}%,${(item.isCompleteHomeworkDay3 / item.studentNumber * 100).toFixed(2)}%\n`
    }
    download(text, 'statistics.csv')
  }

  return <div>
    <button className='btn btn-info btn-soft' onClick={exportData}>download</button>
    <div className='grid grid-cols-2'>
      <CompleteStatusPerDay data={data}/>
    </div>
    <div className="overflow-x-auto">
      <table className="table table-sm">
        <thead>
          <tr>
            <th className='min-w-30'>ip</th>
            <th>期数</th>
            <th>状态</th>
            <th>学员数</th>
            <th>延期</th>
            <th>成单数</th>
            <th>转化率</th>
            <th>Day2课上</th>
            <th>Day2课下</th>
            <th>Day3课上</th>
            <th>Day3课下</th>
            <th>Day4课上</th>
            <th>Day4课下</th>
            <th>填写部分客户画像</th>
            <th>填写了客户画像</th>
            <th>抖音截图分析</th>
            <th>第一节课到课</th>
            <th>第一节课完课</th>
            <th>第二节课到课</th>
            <th>第二节课完课</th>
            <th>第三节课到课</th>
            <th>第三节课完课</th>
            <th>第四节课到课</th>
            <th>第四节课完课</th>
            <th>第一节课作业</th>
            <th>第二节课作业</th>
            <th>第三节课作业</th>
          </tr>
        </thead>
        <tbody>
          {statisticsData.map((item, index) => {
            return <tr key={index}>
              <th>{item.ip}</th>
              <th>{item.courseNo == 0 ? '汇总' : item.courseNo}</th>
              <td>{dayjs().hour(0).minute(0).second(0).diff(dayjs(String(item.courseNo), 'YYYYMMDD'), 'day') > 4 ? '结课' : '行课'}</td>
              <td>{item.studentNumber}</td>
              <td className='text-right'>{item.delay} | {(item.delay / item.studentNumber * 100).toFixed(2)}%</td>
              <td>{item.paid}</td>
              <td>{(item.paid / item.studentNumber * 100).toFixed(2)}%</td>
              <td>{item.paidDuringClassDay2} | {(item.paidDuringClassDay2 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{item.paidNotDuringClassDay2} | {(item.paidNotDuringClassDay2 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{item.paidDuringClassDay3} | {(item.paidDuringClassDay3 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{item.paidNotDuringClassDay3} | {(item.paidNotDuringClassDay3 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{item.paidDuringClassDay4} | {(item.paidDuringClassDay4 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{item.paidNotDuringClassDay4} | {(item.paidNotDuringClassDay4 / item.studentNumber * 100).toFixed(1)}%</td>
              <td>{(item.fillAnyUserSlots / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.fillUserSlots / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.douyinScreenShotAnalysis / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isAttendCourseDay1 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteCourseDay1 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isAttendCourseDay2 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteCourseDay2 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isAttendCourseDay3 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteCourseDay3 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isAttendCourseDay4 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteCourseDay4 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteHomeworkDay1 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteHomeworkDay2 / item.studentNumber * 100).toFixed(0)}%</td>
              <td>{(item.isCompleteHomeworkDay3 / item.studentNumber * 100).toFixed(0)}%</td>
            </tr>
          })}
        </tbody>
      </table>
    </div>
  </div>
}

interface StatisticsData {
  ip:string
  courseNo:number
  studentNumber:number
  delay:number
  paid:number
  paidDuringClassDay2:number
  paidNotDuringClassDay2:number
  paidDuringClassDay3:number
  paidNotDuringClassDay3:number
  paidDuringClassDay4:number
  paidNotDuringClassDay4:number
  fillAnyUserSlots:number
  fillUserSlots:number
  douyinScreenShotAnalysis:number
  isAttendCourseDay1:number
  isAttendCourseDay2:number
  isAttendCourseDay3:number
  isAttendCourseDay4:number
  isCompleteCourseDay1:number
  isCompleteCourseDay2:number
  isCompleteCourseDay3:number
  isCompleteCourseDay4:number
  isCompleteHomeworkDay1:number
  isCompleteHomeworkDay2:number
  isCompleteHomeworkDay3:number
}

interface ColumnFilter {
  id: string
  value: unknown
}