'use client'
import { UserData } from '@/app/type/user'
import { useState } from 'react'
import { toast } from 'react-toastify'
import { IChattingFlag } from 'yuhe/state/user_flags'
import dayjs from 'dayjs'

export function ChatEdit<T>({
  chat,
  changeCourseNo,
  changeNextStage,
  changePhone,
  stageOption,
  updateIsPaid,
  updateIsInviteGroupAfterPayment,
  updateIp,
  updatePayTime,
  clearCache,
  resetSop,
  updatePaymentNumber
}: {
  chat:UserData,
  changeNextStage(chatId: string, stage: T): Promise<void>,
  changePhone(chatId: string, phone: string): Promise<void>,
  changeCourseNo(chatId: string, courseNo: number): Promise<void>,
  clearCache(chatId:string):Promise<void>
  updateIsPaid(chatId:string, isPaid:boolean): Promise<void>
  updateIsInviteGroupAfterPayment(chatId:string, isInviteGroupFailAfterPayment:boolean): Promise<void>
  updateIp(chatId:string, ip:string):Promise<void>
  updatePayTime(chatId:string, time:string): Promise<void>
  resetSop(chatId:string): Promise<void>
  updatePaymentNumber(chatId:string, number:number):Promise<void>
  stageOption:string[]
}) {
  const [stage, setStage] = useState<string>(chat.chat_state.nextStage)
  const [phone, setPhone] = useState<string>(chat.phone ?? '')
  const [courseNo, setCourseNo] = useState<string> (`${chat.course_no ?? ''}`)
  const [isPaid, setIsPaid] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_complete_payment ?? false)
  const [payTime, setPayTime] = useState<string>(dayjs(chat.pay_time).format('YYYY-MM-DDTHH:mm:ss'))
  const [isInviteGroupFailAfterPayment, setIsInviteGroupFailAfterPayment] = useState<boolean>((chat.chat_state.state as IChattingFlag).is_invite_group_fail_after_payment ?? false)
  const [ip, setIp] = useState<string>(chat.ip ?? '')
  const [loading, setLoading] = useState<boolean>(false)
  const [paymentNumber, setPaymentNumber] = useState<number>((chat.chat_state.state as IChattingFlag).payment_number ?? 0)

  return <div>
    <div className='text-2xl p-2'>编辑: {chat.contact.wx_name}</div>
    <form className='flex gap-2 items-center' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeNextStage(chat.id, stage as T), {
        pending: 'change next stage pending',
        success: 'change next stage success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>切换阶段</label>
      <select className='select focus-within:outline-0' disabled={loading} value={stage} onChange={(e) => {
        setStage(e.currentTarget.value)
      }}>
        {stageOption.map((item, index) => {
          return <option key={index}>{item}</option>
        })}
      </select>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>切换</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changePhone(chat.id, phone), {
        pending: 'change phone pending',
        success: 'change phone success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>绑定手机号</label>
      <input type="text" className='input focus-within:outline-0' value={phone} onChange={(e) => { setPhone(e.currentTarget.value) }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>绑定</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(changeCourseNo(chat.id, Number(courseNo)), {
        pending: 'change courseNo pending',
        success: 'change courseNo success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>修改课程号</label>
      <input type="number" className='input focus-within:outline-0' value={courseNo} onChange={(e) => { setCourseNo(e.currentTarget.value) }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>修改</button>
    </form>
    <form className='flex gap-2 items-center mt-2' onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      toast.promise(updateIp(chat.id, ip), {
        pending: 'change courseNo pending',
        success: 'change courseNo success',
        error: {
          render:(e) => {
            return `error: ${e.data}`
          }
        }
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <label className='label w-40'>修改ip</label>
      <input type="text" className='input focus-within:outline-0' value={ip} onChange={(e) => { setIp(e.currentTarget.value) }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading}>修改</button>
    </form>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改成单状态</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isPaid} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateIsPaid(chat.id, e.currentTarget.checked)
          await clearCache(chat.id)
        }, {
          pending: 'change payment state pending',
          success: 'change payment state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsPaid(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改下单时间</label>
      <input type='datetime-local' step={1} className='input disabled:btn-disabled focus-within:outline-0' value={payTime} disabled={loading} onChange={(e) => {
        setPayTime(e.currentTarget.value)
      }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading} onClick={(e) => {
        setLoading(true)
        toast.promise(updatePayTime(chat.id, payTime), {
          pending:'update pay time pending',
          success:'update pay time success',
          error:'update pay time error'
        }).finally(() => {
          setLoading(false)
        })
      }}>修改</button>
    </div>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改下单单数</label>
      <input type='number' step={1} className='input disabled:btn-disabled focus-within:outline-0' value={paymentNumber} disabled={loading} onChange={(e) => {
        setPaymentNumber(e.currentTarget.valueAsNumber)
      }}/>
      <button type="submit" className='btn disabled:btn-disabled' disabled={loading} onClick={(e) => {
        setLoading(true)
        toast.promise(updatePaymentNumber(chat.id, paymentNumber), {
          pending:'update payment number pending',
          success:'update payment number success',
          error:'update payment number error'
        }).finally(() => {
          setLoading(false)
        })
      }}>修改</button>
    </div>
    <div className='flex gap-2 items-center mt-2' >
      <label className='label w-40'>修改拉群是否成功状态</label>
      <input type='checkbox' className='toggle toggle-success disabled:btn-disabled' checked={isInviteGroupFailAfterPayment} disabled={loading} onChange={(e) => {
        e.preventDefault()
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(async() => {
          await updateIsInviteGroupAfterPayment(chat.id, e.currentTarget.checked)
          await clearCache(chat.id)
        }, {
          pending: 'change payment state pending',
          success: 'change payment state success',
          error: {
            render:(e) => {
              return `error: ${e.data}`
            }
          }
        }).then(() => {
          setIsInviteGroupFailAfterPayment(checked)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
    </div>
    <div className='flex gap-2'>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(clearCache(chat.id), {
          pending: 'clear pending',
          success: 'clear success',
          error: 'clear error'
        }).finally(() => {
          setLoading(false)
        })
      }}>clear cache</button>
      <button className='btn btn-neutral focus-within:outline-0 disabled:btn-disabled' disabled={loading} onClick={() => {
        setLoading(true)
        toast.promise(resetSop(chat.id), {
          pending: 'reset pending',
          success: 'reset success',
          error: 'reset error'
        }).finally(() => {
          setLoading(false)
        })
      }}>reset sop</button>
    </div>
  </div>
}