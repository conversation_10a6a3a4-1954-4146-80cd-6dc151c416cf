import UserEdit from '@/app/component/user/edit'
import { Node } from 'moer_overseas/workflow/nodes/types'
import { queryChatById, changeCourseNo, changeNextStage, changePhone } from '@/app/moer_overseas/api/chat'
import { notImplement } from '@/lib/not_implement'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={decodeURIComponent(param.id)}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(Node)}
    updateIsPaid={notImplement}
    updateIsInviteGroupAfterPayment={notImplement}
    updateIp={notImplement}
    clearCache={notImplement}
    resetSop={notImplement}
    updatePaymentNumber={notImplement}
  />
}