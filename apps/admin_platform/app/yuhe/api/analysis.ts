'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import { IChattingFlag } from 'yuhe/state/user_flags'
import { AnalysisData } from '@/app/type/analysis'
import { contentWithFrequency } from 'service/local_cache/type'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { isFillAnyUserSlots, isFillUserSlots } from 'yuhe/visualized_sop/visualized_sop_variable'
import { DataService } from 'yuhe/helper/getter/get_data'
import dayjs from 'dayjs'
import { accountToName } from 'yuhe/config/config'
import { chatStateStoreClient } from 'yuhe/service/instance'

export async function getAnalysisData(startCourseNo:number, endCourseNo:number):Promise<AnalysisData[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const getChatInfo = async(startCourseNo:number, endCourseNo:number) => {
    return await mongoClient.chat.findMany({ where:{ OR:[{ course_no:{ gte:startCourseNo, lte:endCourseNo } }, { course_no_ori:{ gte:startCourseNo, lte:endCourseNo } }]  }, select:{ id:true, wx_id:true, contact:true, chat_state:true, course_no:true, course_no_ori:true, phone:true, ip:true, pay_time:true } })
  }
  const getAccounts = async() => {
    const mongoConfigClient = AdminPrismaMongoClient.getConfigInstance()
    return await mongoConfigClient.config.findMany({ where:{ enterpriseName:'yuhe' }, select:{ accountName:true, wechatId:true } })
  }

  const [chatInfo, accounts] = await Promise.all([getChatInfo(startCourseNo, endCourseNo), getAccounts()])
  const accountMap = new Map<string, string>()
  for (const account of accounts) {
    accountMap.set(account.wechatId, account.accountName)
  }
  const filtedChatInfo = chatInfo.filter((item) => item.wx_id != '****************')

  const ipTable = await mongoClient.ip_table.findMany({ where:{ OR:[{ start_course_no:{ lte:endCourseNo } }, { end_course_no:{ gte:startCourseNo } }] } })

  return filtedChatInfo.filter((chat) => !['****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************'].includes(chat.contact.wx_id)).map((chat) => {
    const state = chat.chat_state.state as IChattingFlag
    const userSlots = UserSlots.fromRecord(chat.chat_state.userSlots as Record<string, contentWithFrequency>)
    const isPaid = state.is_complete_payment ?? false
    const paymentNumber = isPaid ? (state.payment_number ?? 1) : 0
    return {
      chatId:chat.id,
      name: chat.contact.wx_name,
      phone: chat.phone ?? '',
      ip:chat.ip ?? ipTable.find((item) => item.account == chat.wx_id && item.start_course_no <= (chat.course_no_ori ?? chat.course_no ?? 0) && (chat.course_no_ori ?? chat.course_no ?? 0) <= item.end_course_no)?.ip ?? '',
      paidTime:chat.pay_time,
      paymentNumber: paymentNumber,
      isInviteGroupFailAfterPayment:state.is_invite_group_fail_after_payment ?? false,
      assistant: accountMap.get(chat.wx_id) ?? '未知',
      assistantName: accountToName[chat.wx_id] ?? '未知',
      chatNumber: (chat.chat_state.nodeInvokeCount as Record<string, number>).UserMessage ?? 0,
      isPaid: isPaid,
      courseNo: chat.course_no ?? 0,
      courseNoOri:chat.course_no_ori ?? chat.course_no ?? 0,
      isDelay: chat.course_no_ori != null,
      isFillAnyUserSlots:isFillAnyUserSlots(userSlots),
      isFillUserSlots:isFillUserSlots(userSlots),
      isCompleteDouyinAnalysis:state.is_complete_douyin_analysis ?? false,
      isCompleteHomework1: state.is_complete_homework1 ?? false,
      isCompleteHomework2: state.is_complete_homework2 ?? false,
      isCompleteHomework3: state.is_complete_homework3 ?? false,
      isAttendCourseDay1: state.is_attend_course_day1 ?? false,
      isAttendCourseDay2: state.is_attend_course_day2 ?? false,
      isAttendCourseDay3: state.is_attend_course_day3 ?? false,
      isAttendCourseDay4: state.is_attend_course_day4 ?? false,
      isCompleteCourseDay1: state.is_complete_course_day1 ?? false,
      isCompleteCourseDay2: state.is_complete_course_day2 ?? false,
      isCompleteCourseDay3: state.is_complete_course_day3 ?? false,
      isCompleteCourseDay4: state.is_complete_course_day4 ?? false,

      isPaidDuringClassDay2:judgeIsPaidDuringClassDay2(chat),
      isPaidNotDuringClassDay2:judgeIsPaidNotDuringClassDay2(chat),
      isPaidDuringClassDay3:judgeIsPaidDuringClassDay3(chat),
      isPaidNotDuringClassDay3:judgeIsPaidNotDuringClassDay3(chat),
      isPaidDuringClassDay4:judgeIsPaidDuringClassDay4(chat),
      isPaidNotDuringClassDay4:judgeIsPaidNotDuringClassDay4(chat),
    }
  })
  function judgeIsPaidDuringClassDay2(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isBefore(day0.add(2, 'day').hour(23))) {
      return true
    } else {
      return false
    }
  }
  function judgeIsPaidNotDuringClassDay2(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isAfter(day0.add(2, 'day').hour(23)) && payTime.isBefore(day0.add(3, 'day').hour(19))) {
      return true
    } else {
      return false
    }
  }
  function judgeIsPaidDuringClassDay3(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isAfter(day0.add(3, 'day').hour(19)) && payTime.isBefore(day0.add(3, 'day').hour(23))) {
      return true
    } else {
      return false
    }
  }
  function judgeIsPaidNotDuringClassDay3(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isAfter(day0.add(3, 'day').hour(23)) && payTime.isBefore(day0.add(4, 'day').hour(19))) {
      return true
    } else {
      return false
    }
  }
  function judgeIsPaidDuringClassDay4(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isAfter(day0.add(4, 'day').hour(19)) && payTime.isBefore(day0.add(4, 'day').hour(23))) {
      return true
    } else {
      return false
    }
  }
  function judgeIsPaidNotDuringClassDay4(chat:Awaited<ReturnType<typeof getChatInfo>>[number]):boolean {
    if (!chat.pay_time) return false
    const state = chat.chat_state.state as IChattingFlag
    const isPaid = state.is_complete_payment ?? false
    if (!isPaid) return false
    const payTime = dayjs(chat.pay_time)
    const day0 = dayjs(String(chat.course_no), 'YYYYMMDD').hour(0).minute(0).second(0)
    if (payTime.isAfter(day0.add(4, 'day').hour(23))) {
      return true
    } else {
      return false
    }
  }
}


export async function updateAttendAndCompleteCourseData() {
  const threeDaysAgo = Number(dayjs().subtract(3, 'day').format('YYYYMMDD'))
  console.log(threeDaysAgo)
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const users = (await mongoClient.chat.findMany({ where:{ course_no:{ gte:threeDaysAgo } } })).filter((user) => user.phone)
  for (let i = 0; i < users.length; i += 10) {
    chatStateStoreClient.clearCache(users[i].id)
    await Promise.allSettled(users.slice(i, i + 10).map((user) => Promise.allSettled([
      DataService.isAttendCourse(user.id, 1),
      DataService.isAttendCourse(user.id, 2),
      DataService.isAttendCourse(user.id, 3),
      DataService.isAttendCourse(user.id, 4),
      DataService.isCompletedCourse(user.id, 1),
      DataService.isCompletedCourse(user.id, 2),
      DataService.isCompletedCourse(user.id, 3),
      DataService.isCompletedCourse(user.id, 4),
    ])))
  }
}
