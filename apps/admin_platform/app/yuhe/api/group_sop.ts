'use server'

import {
  ActionType,
  getVisualizedRedisGroupSopKey,
  GroupAction,
  GroupSituation,
  IGroupTask,
  LinkSourceType,
  TextType
} from 'service/visualized_sop/visualized_sop_type'
import { Config } from 'config'
import {
  actionCustomMap,
  conditionJudgeMap,
  linkSourceVariableTagMap,
  textVariableMap
} from 'yuhe/visualized_sop/visualized_sop_variable'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { AdminPrismaMongoClient } from '@/lib/prisma'
import { GroupSop } from '@/app/type/group_sop'
import { sleep } from 'lib/schedule/schedule'
import {
  groupActionCustomMap,
  groupConditionJudgeMap,
  groupLinkSourceVariableTagMap,
  groupTextVariableMap
} from 'yuhe/visualized_sop/visualized_group_sop_variable'
import { RedisDB } from 'model/redis/redis'
import { queryAllGroup } from './group'
import { queryAllGroupTags } from './group_sop_tag'
import { queryAllGroupSopTopics } from './group_sop_topic'
import {
  getVisualizedGroupSopQueueName,
  startGroupTasks,
  VisualizedGroupSopTasks
} from 'service/visualized_sop/visualized_group_sop_task_starter'
import { calGroupTaskTime } from 'yuhe/helper/getter/get_data'
import { queryAccounts } from './account'
import { Queue } from 'bullmq'
import { yuheVisualizedGroupSopProcessor } from 'yuhe/service/instance'

const testGroupAccount = '****************'
const testGroupId = 'R:*****************'

export async function queryGroupSop({
  page,
  pageSize,
  tag,
  topic
}: {
  page: number;
  pageSize: number;
  tag: string
  topic: string
}):Promise<GroupSop[]> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sops = await mongoClient.group_sop.findMany({
    where:{
      tag,
      topic
    },
    take: pageSize,
    skip: pageSize * (page - 1),
    orderBy: [{ week: 'asc' }, { day:'asc' }, { time: 'asc' }],
  })
  return sops as GroupSop[]
}

export async function queryGroupSopById(
  id:string
):Promise<GroupSop | null> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sop = await mongoClient.group_sop.findFirst({
    where:{
      id
    }
  })
  return sop as (GroupSop | null)
}

export async function queryGroupSopCount({ tag, topic }: {tag:string, topic:string}) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const count = await mongoClient.group_sop.count({ where:{ tag, topic } })
  return count
}

export async function getEnableGroupSops() {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  return await mongoClient.group_sop.findMany({ where: { enable: true } })
}

export async function updateGroupSopMq() {
  'use server'
  Config.setting.projectName = 'yuhe'
  const groups = await queryAllGroup()
  const accounts = await queryAccounts()
  for (const account of accounts) {
    await VisualizedGroupSopTasks.clearSop('yuhe', account.wechatId)
  }
  for (const group of groups) {
    await startGroupTasks(AdminPrismaMongoClient.getYuheInstance(), 'yuhe', group.group_id, calGroupTaskTime)
  }
}

export async function validateGroupSops(): Promise<string> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sops = await mongoClient.group_sop.findMany()
  let error = ''
  for (const sop of sops) {
    if (!sop) continue
    error += validateGroupSop(sop as GroupSop)
  }
  return error
}

function validateGroupSop(sop: GroupSop) {
  let isError = false
  let content = `sop: ${sop.title}\n`
  if (sop.day < 1 || sop.day > 7) {
    isError = true
    content += `day不合法，day是${sop.day}\n`
  }
  const splitedTime = sop.time.split(':')
  //判断时间字符串是否合法
  if (
    splitedTime.length != 3 ||
    splitedTime[0].length != 2 ||
    Number(splitedTime[0]) < 0 ||
    Number(splitedTime[0]) > 24 ||
    splitedTime[1].length != 2 ||
    Number(splitedTime[1]) < 0 ||
    Number(splitedTime[1]) > 60 ||
    splitedTime[2].length != 2 ||
    Number(splitedTime[2]) < 0 ||
    Number(splitedTime[2]) > 60
  ) {
    isError = true
    content += `time不合法，time是${sop.time}\n`
  }
  for (const situation of sop.situations) {
    for (const condition of situation.conditions) {
      if (!conditionJudgeMap[condition.condition]) {
        isError = true
        content += `条件${condition.condition}不存在\n`
      }
    }
    for (const action of situation.action) {
      if (action.type == ActionType.text) {
        if (!action.description) {
          isError = true
          content += '没有填写description\n'
        }
        for (const text of action.textList) {
          if (text.type == TextType.variable) {
            if (!textVariableMap[text.tag]) {
              isError = true
              content += `字符串变量${text.tag}不存在\n`
            }
          }
        }
      } else if (action.type == ActionType.custom) {
        if (!actionCustomMap[action.tag]) {
          isError = true
          content += `自定义事件变量${action.tag}不存在\n`
        }
      } else if (action.type == ActionType.link) {
        if (
          action.source.type == LinkSourceType.variable &&
          !linkSourceVariableTagMap[action.source.tag]
        ) {
          isError = true
          content += `链接变量${action.source.tag}不存在\n`
        }
      }
    }
  }
  if (isError) {
    return `${content}\n`
  } else {
    return ''
  }
}

export async function testGroupAction(id: string) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sop = await mongoClient.group_sop.findFirst({ where: { id } })
  if (!sop) {
    throw '没有这个sop'
  }
  Config.setting.wechatConfig = await loadConfigByWxId(testGroupAccount)
  for (const { action } of sop.situations) {
    for (const singleAction of action) {
      await yuheVisualizedGroupSopProcessor.handleAction(testGroupId, singleAction as GroupAction, {
        sop_id:id
      })
    }
  }
}

export async function testGroupSop(sopId:string) {
  Config.setting.wechatConfig = await loadConfigByWxId(testGroupAccount)
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sop = await mongoClient.group_sop.findFirst({ where: { id:sopId } })
  if (!sop) {
    throw '没有这个sop'
  }
  await yuheVisualizedGroupSopProcessor.handleSop(testGroupId, sop as GroupSop)
}

export async function updateRedisGroupSop() {
  'use server'
  const redisClient = RedisDB.getInstance()
  const sops = await getEnableGroupSops()
  const groups = await queryAllGroup()
  const tags = (await queryAllGroupTags()).filter((tag) => tag.enable)
  const topics = (await queryAllGroupSopTopics()).filter((topic) => topic.enable)

  for (const group of groups) {
    const tagsOfAccount = tags.filter((tag) => tag.enable_group.includes(group.group_id))
    const topicsOfAccount = topics.filter((topic) => tagsOfAccount.map((tag) => tag.name).includes(topic.tag))
    const finalSops = sops.filter((sop) => tagsOfAccount.map((tag) => tag.name).includes(sop.tag) && topicsOfAccount.map((topic) => topic.name).includes(sop.topic))
    await redisClient.set(getVisualizedRedisGroupSopKey('yuhe', group.group_id), JSON.stringify(finalSops))
  }
}

export async function testAllGroupSop(tag:string, topic:string) {
  Config.setting.wechatConfig = await loadConfigByWxId(testGroupAccount)
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sops = await mongoClient.group_sop.findMany({ where:{ enable:true, tag, topic }, orderBy:[{ week:'asc' }, { day:'asc' }, { time:'asc' }] })
  for (const sop of sops) {
    await yuheVisualizedGroupSopProcessor.sendGroupMsg(testGroupId, `发送sop${sop.title} ${sop.week}周 ${sop.day}天 ${sop.time}`, undefined)
    await sleep(800)
    for (const { action } of sop.situations) {
      for (const singleAction of action) {
        await yuheVisualizedGroupSopProcessor.handleAction(testGroupId, singleAction as GroupAction, {
          sop_id:sop.id
        })
        await sleep(800)
      }
    }
  }
}

export async function deleteGroupSop(id:string) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop.delete({ where:{ id } })
}

export async function saveGroupSop(sop:Omit<GroupSop, 'id'>) {
  'use server'
  const mongoClienet = AdminPrismaMongoClient.getYuheInstance()
  await mongoClienet.group_sop.create({ data:sop })
}

export async function getGroupConditionJudgeKeys() {
  'use server'
  return Object.keys(groupConditionJudgeMap)
}

export async function getGroupCustomKeys() {
  'use server'
  return Object.keys(groupActionCustomMap)
}

export async function getGroupVariableMapKeys() {
  'use server'
  return Object.keys(groupTextVariableMap)
}

export async function getGroupLinkSourceVariableTagKeys() {
  'use server'
  return Object.keys(groupLinkSourceVariableTagMap)
}

export async function updateGroupSop(sop_id:string, sop:Partial<GroupSop>) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.group_sop.update({ where:{ id:sop_id }, data:sop })
}

export async function copyGroupSop(sop_id:string):Promise<GroupSop> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const sop = await mongoClient.group_sop.findFirst({ where:{ id:sop_id } })
  if (!sop) throw ('没有这个sop')
  const newSop = await mongoClient.group_sop.create({ data:{
    ...sop,
    id:undefined,
    title: `${sop.title}_copy`,
    situations:sop.situations as GroupSituation[]
  } })
  return newSop as GroupSop
}

export async function listGroupSopTask(groupId:string): Promise<IGroupTask[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
  if (!groupInfo) {
    throw ('none group')
  }
  const queue = new Queue<IGroupTask>(getVisualizedGroupSopQueueName('yuhe', groupInfo.owner_account_id), {
    connection: RedisDB.getInstance()
  })
  return (await queue.getJobs('delayed')).map((item) => item.data)
}