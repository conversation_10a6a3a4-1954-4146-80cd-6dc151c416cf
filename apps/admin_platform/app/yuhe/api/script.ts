'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import { DataService } from 'yuhe/helper/getter/get_data'
import { Config } from 'config'
import { RegexHelper } from 'lib/regex/regex'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { changeNextStage, queryChatsWithoutPhone } from './chat'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { contentWithFrequency } from 'service/local_cache/type'
import { Node } from 'yuhe/workflow/nodes/types'
import { XingyanAPI } from 'model/xingyan'

export async function setUserWithoutPhoneStageIntoPhoneQuery(courseNo:number):Promise<void> {
  const users = await queryChatsWithoutPhone(courseNo)
  for (const user of users) {
    Config.setting.wechatConfig = await loadConfigByWxId(user.wx_id)
    let phoneNumber = await DataService.bindPhoneFromRemark(user.id)
    if (!phoneNumber) {
      const phoneNumberFromName = RegexHelper.extractPhoneNumber(user?.contact.wx_name ?? '')
      if (phoneNumberFromName) {
        await DataService.bindPhone(user.id, phoneNumberFromName)
        phoneNumber = phoneNumberFromName
      }
    }
    if (!phoneNumber) {
      await changeNextStage(user.id, Node.PhoneQuery)
    }
  }
}

export async function exportUserSlots(startCourseNo:number, endCourseNo:number):Promise<string> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const users = await mongoClient.chat.findMany({ where:{ course_no:{ gte:startCourseNo, lte:endCourseNo } } })
  let text = '昵称,手机号,原期数,性别,年龄,行业,店面类型,是否抖音在做,年营业额,所在城市\n'
  for (const user of users) {
    const name = user.contact.wx_name
    const userSlots = UserSlots.fromRecord(user.chat_state.userSlots as Record<string, contentWithFrequency>)
    const sex = userSlots.getSubTopicContent('基本信息', '年龄')
    const age = userSlots.getSubTopicContent('基本信息', '性别')
    const industry = userSlots.getSubTopicContent('基本信息', '行业类目')
    const revenue = userSlots.getSubTopicContent('基本信息', '年营业额')
    const shopType = userSlots.getSubTopicContent('基本信息', '店面类别')
    const isDouYinWork = userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做')
    const city = userSlots.getSubTopicContent('基本信息', '所在城市')
    text += `${name},${user.phone},${user.course_no_ori ? user.course_no_ori : user.course_no},${sex},${age},${industry},${shopType},${isDouYinWork},${revenue},${city}\n`
  }
  return text
}

export async function addBoardcastWhiteListByCourseNoIntoCourseNo(userCourseNo:number, destinationCourseNo:number):Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const users = await mongoClient.chat.findMany({ where:{ course_no:userCourseNo } })
  for (const user of users) {
    if (!user.phone) continue
    const botId = user.wx_id
    const xingYanClient:XingyanAPI = XingyanAPI.getXingYanClientByBotId(botId)

    await DataService.addBoardcastWhiteList(
      xingYanClient,
      destinationCourseNo,
      user.contact.wx_name,
      user.phone
    )
  }
}