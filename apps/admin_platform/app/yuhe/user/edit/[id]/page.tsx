import { changeCourseNo, changeNextStage, changePhone, clearCache, queryChatById, updateIp, updateIsInviteGroupAfterPayment, updateIsPaid, updatePaymentNumber } from '@/app/yuhe/api/chat'
import UserEdit from '@/app/component/user/edit'
import { Node } from 'yuhe/workflow/nodes/types'
import { resetSop } from '@/app/yuhe/api/sop'

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = await params
  return <UserEdit
    id={param.id}
    queryChatById={queryChatById}
    changeCourseNo={changeCourseNo}
    changeNextStage={changeNextStage}
    changePhone={changePhone}
    stageOption={Object.values(Node)}
    updateIsPaid={updateIsPaid}
    updateIsInviteGroupAfterPayment={updateIsInviteGroupAfterPayment}
    updateIp={updateIp}
    clearCache={clearCache}
    resetSop={resetSop}
    updatePaymentNumber={updatePaymentNumber}
  />
}