{"name": "admin_platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint"}, "dependencies": {"axios": "^1.10.0", "bullmq": "^5.12.9", "chroma-js": "^3.1.2", "config": "workspace:*", "dayjs": "^1.11.13", "echarts": "^5.6.0", "filepond": "^4.32.7", "filepond-plugin-image-preview": "^4.6.12", "langchain": "^0.3.21", "lib": "workspace:*", "material-react-table": "^3.2.1", "model": "workspace:*", "moer_overseas": "workspace:*", "motion": "^12.16.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "react": "19.1.0", "react-dom": "19.1.0", "react-filepond": "^7.1.3", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "service": "workspace:*", "uuid": "^11.1.0", "yuhe": "workspace:*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.14", "@types/node": "^20", "@types/react": "19.1.6", "@types/react-dom": "19.1.6", "daisyui": "^5.0.6", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4.0.14", "typescript": "^5"}, "overrides": {"@types/react": "19.1.6", "@types/react-dom": "19.1.6"}}