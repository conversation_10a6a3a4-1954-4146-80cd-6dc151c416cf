import axios from 'axios'
import { Retry } from 'lib/retry/retry'
import logger from 'model/logger/logger'
import { ClientAccountConfig } from 'service/database/config'
import { Event } from 'service/message_handler/ycloud/message_sender'


export class EventDispatcher {

  private static async getServerAddress(wechatId: string) {
    const serverAddress = await ClientAccountConfig.getServerAddressByWechatId(wechatId)
    if (!serverAddress) {
      throw new Error('没有找到对应的服务器地址')
    }

    return serverAddress
  }

  private static async dispatchEventToServer(serverAddress: string, event: any, isMsg?: boolean) {
    logger.log(JSON.stringify(event, null, 4))
    if (!serverAddress) {
      console.error('没有找到对应的服务器地址')
      return
    }

    try {
      await Retry.retry(4, async () => {
        await axios.post(`${serverAddress}/${isMsg ? 'message' : 'event'}`, event, { insecureHTTPParser: true })
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 2 * 60 * 1000  // 2分钟
          if (retryCount === 2) return 10 * 60 * 1000 // 10分钟
          if (retryCount === 3) return 30 * 60 * 1000 // 30分钟
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      console.error('事件分发失败：', serverAddress, e)
    }
  }

  static async dispatchMessage(data: any, botId:string) {
    try {
      const serverAddress = await this.getServerAddress(botId)

      await this.dispatchEventToServer(serverAddress, data, true)
    } catch (e) {
      logger.warn(`没有找到对应的地址, bot:${botId}`)
    }
  }

  static async dispatchEvent(data: any, isMsg?: boolean) {
    logger.log('event:', JSON.stringify(data, null, 4))
    if (data.botInfo) {
      const serverAddress = await this.getServerAddress(data.imBotId)

      await this.dispatchEventToServer(serverAddress, data, isMsg)
    }
  }
}