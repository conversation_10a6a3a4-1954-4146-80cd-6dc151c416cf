import { Config } from 'config'
import { CacheDecorator } from 'lib/cache/cache'
import logger from 'model/logger/logger'
import { MoerAPI } from 'model/moer_api/moer'
import { catchGlobalError } from 'model/server/server'
import express from 'express'
import { IMoerEvent } from 'moer_overseas/client/client_server'
import { MoerEventForwardHandler } from 'moer_overseas/client/event_foraward/moer_event_forward'
import { LRUCache } from 'lru-cache'

const app = express()
app.use(express.json())

/**
 * 分发 Moer 的事件到对应的服务器
 */
app.post('/moer/event', async (req, res) => {
  // 接收消息
  const data: IMoerEvent = req.body

  MoerEventForwardHandler.handle(data) // 处理 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})


interface IBindEvent {
  chat_id: string
  phone: string
}

// cache 一下 bindPhone 的请求
const bindPhoneCache =  new LRUCache<string, any>({
  max: 100,
  ttl: 10 * 60 * 1000   // 10 分钟后数据自动删除（单位：毫秒）
})

app.post('/moer/bindPhone', async (req, res) => {
  const data: IBindEvent = req.body
  try {
    if (bindPhoneCache.has(JSON.stringify(data))) {
      res.send({
        code: 200,
        msg: 'ok'
      })
      return
    }

    bindPhoneCache.set(JSON.stringify(data), true) // 10 分钟以内做重复请求的限流

    //手动绑定手机号暂时不需要
    // await bindPhone(data)

    res.send({
      code: 200,
      msg: 'ok'
    })
  } catch (e) {
    logger.error('手机号绑定失败', e)

    res.send({
      code: 500,
      msg: 'error'
    })
  }
})

const serverPort = 4003

catchGlobalError()

app.listen(serverPort, '0.0.0.0', () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务
Config.setting.bindingPhone = true
MoerAPI.getUserById = CacheDecorator.decorateAsync(MoerAPI.getUserById)

// 开启 弹幕事件监听
// TODO:revert danmu helper
// DanmuHelper.processPullDanmu()