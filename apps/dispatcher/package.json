{"name": "dispatcher", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "check-import": "ts-node ../../scripts/check_dependecies.ts", "yuhe:test": "export NODE_ENV=dev WECHAT_NAME=yuhe_test && ts-node client/client_server.ts", "yuhe:online-test": "export NODE_ENV=dev WECHAT_NAME=yuhe_online_test && ts-node client/client_server.ts", "client": "ts-node client/client_server.ts", "yuhe:event:server": "ts-node server/event_server.ts", "yuhe:deploy": "ts-node docker/deploy.ts", "ycloud_server": "ts-node ycloud_server"}, "devDependencies": {"@types/express": "^4.17.19", "@types/inquirer": "^9.0.8", "@types/jest-when": "^3.5.5", "@types/node": "^20.7.0", "inquirer": "^8.2.6", "jest": "^29.7.0", "jest-when": "^3.7.0", "js-yaml": "^4.1.0", "prisma": "^6.9.0", "ts-node": "^10.9.1", "typescript": "5.8.2"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@langchain/core": "^0.3.44", "@prisma/client": "6.6.0", "@types/js-yaml": "^4.0.9", "axios": "^1.5.1", "bullmq": "^5.12.9", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "dayjs": "^1.11.13", "express": "^4.18.2", "langchain": "^0.3.21", "config": "workspace:*", "lib": "workspace:*", "service": "workspace:*", "model": "workspace:*", "moer_overseas": "workspace:*", "lru-cache": "^10.2.0", "openai": "^4.98.0", "p-limit": "3.1.0", "zod": "^3.23.8"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}