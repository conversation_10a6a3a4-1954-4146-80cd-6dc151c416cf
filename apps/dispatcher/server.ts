import express from 'express'
import chalk from 'chalk'
import { EventDispatcher } from './event_dispatch'
import logger from 'model/logger/logger'
import { catchGlobalError } from 'model/server/server'
import { IReceivedMessage, ISendMessageResult } from 'model/juzi/type'

const app = express()
app.use(express.json())

app.get('/', (req, res) => {
  res.send('Hello World!')
})

// 对回调事件进行统一处理
app.post('/wecom/message', async (req, res) => {
  // 接收消息
  const data = req.body as IReceivedMessage
  if (!data.imBotId) {
    logger.error('wecom事件没有imBotId', data)
    res.send('ok')
    return
  }
  await EventDispatcher.dispatchMessage(data, data.imBotId)

  res.send('ok')
})

// 对消息发送失败。进行报警处理
app.post('/wecom/sendResult', async (req, res) => {
  // 接收消息
  const data = req.body as ISendMessageResult
  const chat_id = `${data.imContactId}_${data.imBotId}`

  if (data.errorcode) {
    logger.error('消息发送失败', data, chat_id, data.errorcode, data.errormsg)
  } else {
    logger.trace('消息发送成功', data, chat_id)
  }

  res.send('ok')
})


app.post('/wecom/event', async (req, res) => {
  const data = req.body
  EventDispatcher.dispatchEvent(data)
  res.send('ok')
})

catchGlobalError()

app.listen(6001, '0.0.0.0', () => {
  console.log(chalk.green('事件分发服务器已启动'), chalk.red('原') +
      chalk.greenBright('神') +
      chalk.blueBright('启') +
      chalk.yellowBright('动') +
      chalk.magentaBright('！'))
  console.log('Server is running on port 6001')
})