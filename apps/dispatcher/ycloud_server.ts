import express from 'express'
import chalk from 'chalk'
import { EventDispatcher } from './event_dispatch'
import logger from 'model/logger/logger'
import { catchGlobalError } from 'model/server/server'
import { Event } from 'service/message_handler/ycloud/message_sender'
import dayjs from 'dayjs'
import { PrismaMongoClient } from 'moer_overseas/helper/mongodb/prisma'

const app = express()
app.use(express.json())

app.get('/', (req, res) => {
  res.send('Hello World!')
})

// 对回调事件进行统一处理
app.post('/ycloud/message', async (req, res) => {
  // 接收消息
  const data:Event = req.body
  if (data.type != 'whatsapp.inbound_message.received' || !data.whatsappInboundMessage?.from || !data.whatsappInboundMessage?.to) {
    logger.error('消息类型不对')
    res.send('ok')
    return
  }
  await EventDispatcher.dispatchMessage(data, data.whatsappInboundMessage.to)

  res.send('ok')
})

// 对消息发送失败。进行报警处理
app.post('/ycloud/sendResult', async (req, res) => {
  // 接收消息
  const data:Event = req.body
  if (data.type != 'whatsapp.message.updated' || !data.whatsappMessage?.from || !data.whatsappMessage?.to) {
    logger.error('消息类型不对')
    res.send('ok')
    return
  }
  const chat_id = `${data.whatsappMessage.to}_${data.whatsappMessage.from}`

  const status = data.whatsappMessage.status
  if (status == 'sent' || status == 'delivered' || status == 'read' || status == 'accepted') {
    const messageId = data.whatsappMessage.wamid
    const mongodbClient = PrismaMongoClient.getCommonInstance()
    await mongodbClient.chat_history.upsert({
      where:{ message_id:messageId },
      update:{ state:status },
      create:{
        chat_id:chat_id,
        content:data.whatsappMessage.text?.body ?? '',
        created_at:dayjs(data.whatsappMessage.deliverTime).toDate(),
        role:'assistant',
        is_send_by_human:true,
        message_id:data.whatsappMessage.wamid,
        state:status
      } })
    logger.trace('消息状态修改', data, chat_id)
  } else {
    logger.error('消息发送失败', data, chat_id)
  }

  res.send('ok')
})

catchGlobalError()
const port = 3001

app.listen(port, '0.0.0.0', () => {
  console.log(chalk.green('ycloud事件分发服务器已启动'), chalk.red('原') +
      chalk.greenBright('神') +
      chalk.blueBright('启') +
      chalk.yellowBright('动') +
      chalk.magentaBright('！'))
  console.log(`Server is running on port ${port}`)
})
