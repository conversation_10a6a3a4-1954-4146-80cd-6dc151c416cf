import { IChattingFlag } from '../../../state/user_flags'
import { DataService } from '../../../helper/getter/get_data'
import { isScheduleTimeAfter } from '../../../helper/tool/creat_schedule_task'
import { WhatsappMessageMedia } from 'service/message_handler/ycloud/message_sender'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../../schedule/type'
import {
  chatHistoryServiceClient,
  chatStateStoreClient,
  yCloudCommonMessageSender,
  yCloudMessageSender
} from '../../../service/instance'
import { UserLanguage } from '../../../helper/language/user_language_verify'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { sleep } from 'lib/schedule/schedule'


export class PreCourseCompletionTask {

  public static async sendPreCourseCompleteGift(chatId: string) {
    // 完课礼只发一次
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_send_pre_course_completion_gift || (await chatHistoryServiceClient.getFormatChatHistoryByChatId(chatId)).includes('[冥想练习指南图片]')) {
      return // 已经发送过小讲堂礼物
    }

    const currentTime = await DataService.getCurrentTime(chatId)

    // 周一之后不发送完课礼了
    if (isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })) {
      return
    }

    // 如果完成了，发放完课礼
    await chatStateStoreClient.update(chatId, {
      state:<IChattingFlag> {
        is_send_pre_course_completion_gift: true
      }
    })

    await PreCourseCompletionTask.sendMessage(chatId)

    //发送能量测评
    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_delayed_send_energy_test) {
      return
    }

    await SilentReAsk.schedule(TaskName.sendEnergyTest, chatId, 1000 * 60 * 60 * 24 * 7)
  }


  private static async sendMessage(chatId: string) {
    const language = await UserLanguage.getLanguage(chatId)
    let msg = ''
    let imageLink = '' //冥想练习指南图片链接
    if (language === UserLanguage.Language_EN) {
      msg = 'we\'ve wrapped up our introductory course, here’s a “Beginner’s Guide to Meditation.” Pay special attention to Part 2 on posture—it’ll help you hit the ground running in the first class'
      imageLink = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/en/%E5%86%A5%E6%83%B3%E7%BB%83%E4%B9%A0%E6%8C%87%E5%8D%97.jpeg'
    } else if (language === UserLanguage.Language_ZH) {
      msg = '看到我們完成小講堂啦，送您《冥想入門指南》，可以特別留意第二部分「冥想的姿勢」，下周一更快進入狀態喔！'
      imageLink = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/more_oversea/classReward/cn/%E5%B0%8F%E8%AE%B2%E5%A0%82%E5%A5%96%E5%8A%B1.jpg'
    }

    await yCloudCommonMessageSender.sendText(chatId, {
      text: msg,
      description:'[小讲堂完成后提醒]',
      type:SendMessageType.text
    })

    await sleep(3000)

    await yCloudCommonMessageSender.sendImage(chatId, {
      url:imageLink,
      description:'[冥想入门指南图片]',
      type:SendMessageType.image
    })

  }

}