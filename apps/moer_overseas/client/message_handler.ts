import { getChatId, getUserId } from 'config/chat_id'
import { LLM } from 'lib/ai/llm/llm_model'
import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import { IWecomReceivedMsgType } from 'model/juzi/type'
import logger from 'model/logger/logger'
import { Config } from 'config'
import { AliyunCredentials } from 'lib/cer'
import OpenAI from 'openai'
import axios from 'axios'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { WhatsappInboundMessage } from 'service/message_handler/ycloud/message_handler'
import { eventTrackClient } from '../service/instance'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB
const MAX_VIDEO_DUATION = 40 // 秒

// Initialize Aliyun credentials
AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})

export async function handleImageMessage(imageUrl: string, chatId: string) {
  const response = await new LLM({
    temperature: 0,
    max_tokens: 200,
    meta: { promptName: 'image_caption', chat_id: chatId, description: '图片转文本' }
  }).imageChat(imageUrl, `# 图片描述
你是一名抖音流量课导师，正与客户聊天。客户发来一张图片，请先分类图片，然后用一段话解释图片内容

## 分类规则
1. 视为普通图片

## 输出格式
- 若为普通图片，文本以“【普通图片】”开头，然后正常描述图片内容`)

  eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
    image_url: imageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Image) })
  return `${response}`
}

export async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}

export async function handleVideoMessage(videoUrl: string, chatId: string) {
  const userId = getUserId(chatId)
  const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

  const openai = new OpenAI({
    apiKey: dashscopeApiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  })

  try {
    const sizeBytes = await getVideoFileSize(videoUrl)
    if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
      throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
    }
    const messages: any = [
      {
        'role': 'user',
        'content': [{
          'type': 'video_url',
          'video_url': { 'url': videoUrl },
        },
        { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么，输出一段话，请不要使用markdown格式' }]
      }]
    const qwenResponse = await openai.chat.completions.create({
      model: 'qwen-omni-turbo',
      messages: messages,
      max_completion_tokens: 512,
      stream: true,
      stream_options: {
        include_usage: true
      },
      modalities: ['text']
    })
    let qwenResponseText = ''
    for await (const chunk of qwenResponse) {
      qwenResponseText += chunk.choices[0]?.delta.content || ''
    }
    qwenResponseText = qwenResponseText.trim()
    if (!qwenResponseText.startsWith('【视频】')) { qwenResponseText = `【视频】${qwenResponseText}` }
    qwenResponseText = qwenResponseText.replace(/\n/g, '')

    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(
        HumanTransferType,
        HumanTransferType.UnknownMessageType
      ),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(
        IWecomReceivedMsgType,
        IWecomReceivedMsgType.Video
      ),
    })

    return qwenResponseText
  } catch (error) {
    logger.warn(`处理视频消息时出错: ${error}`)
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.ProcessVideoFailed, true)
    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
    })
    return ''
  }
}

export async function handleUnknownMessage(message: WhatsappInboundMessage) {
  if (!message.from) { return }

  const chat_id = getChatId(message.from)
  await HumanTransfer.transfer(chat_id, message.from, HumanTransferType.UnknownMessageType)

  eventTrackClient.track(chat_id,
    IEventType.TransferToManual,
    { reason: ObjectUtil.enumValueToKey(HumanTransferType,  HumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.type)
    })

  logger.warn(`未知消息，请及时处理：${message.type}`)
}