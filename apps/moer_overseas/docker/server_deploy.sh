#!/bin/bash

# MOER_OVERSEAS 服务器部署脚本
# 用法: ./server_deploy.sh [service1] [service2] ...
# 如果没有指定服务，则部署所有服务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_dependencies() {
    log "检查依赖..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装或不在 PATH 中"
        exit 1
    fi

    log_success "依赖检查通过"
}

# 拉取最新镜像
pull_images() {
    log "拉取最新镜像..."
    docker-compose pull || docker compose pull
    log_success "镜像拉取完成"
}

# 部署单个服务
deploy_single_service() {
    local service="$1"

    log "部署服务: $service"

    # 停止并删除现有容器（如果存在）
    if command -v docker-compose &> /dev/null; then
        docker-compose stop "$service" 2>/dev/null || true
        docker-compose rm -f "$service" 2>/dev/null || true
        docker-compose up -d "$service"
    else
        docker compose stop "$service" 2>/dev/null || true
        docker compose rm -f "$service" 2>/dev/null || true
        docker compose up -d "$service"
    fi

    log_success "服务 $service 部署完成"
}

# 健康检查函数（单个服务）
health_check() {
    local service="$1"

    log "开始对服务 $service 进行健康检查..."

    # 等待容器启动
    log "等待容器启动（5秒）..."
    sleep 5

    # 获取容器ID
    local container_id
    if command -v docker-compose &> /dev/null; then
        container_id=$(docker-compose ps -q "$service" 2>/dev/null)
    else
        container_id=$(docker compose ps -q "$service" 2>/dev/null)
    fi

    if [ -z "$container_id" ]; then
        log_error "无法找到服务 $service 的容器ID"
        return 1
    fi

    # 检查容器状态
    local status=$(docker inspect -f '{{.State.Status}}' "$container_id" 2>/dev/null)
    log "容器 $service 状态: $status"

    if [ "$status" != "running" ]; then
        log_error "容器 $service 未处于运行状态 (当前: $status)"
        return 1
    fi

    # 检查端口可访问性
    local exposed_port=$(docker port "$container_id" | grep -o -m 1 '[0-9]\{4\}' | head -1)

    if [ -z "$exposed_port" ]; then
        log_warning "无法检测服务 $service 的暴露端口，跳过端口检查"
        log_success "服务 $service 容器状态正常"
        return 0
    fi

    log "检查端口可访问性: $exposed_port"

    # 重试机制
    local retry_count=0
    local max_retries=5
    local retry_delay=4

    while [[ $retry_count -lt $max_retries ]]; do
        if curl --fail --silent --show-error --max-time 5 "http://localhost:$exposed_port" > /dev/null 2>&1; then
            log_success "健康检查通过：服务 $service 在 http://localhost:$exposed_port 可访问"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [[ $retry_count -lt $max_retries ]]; then
                log_warning "健康检查失败：服务 $service 在端口 $exposed_port 不可访问。将在 ${retry_delay} 秒后重试... ($retry_count/$max_retries)"
                sleep $retry_delay
            else
                log_error "服务 $service 在端口 $exposed_port 的健康检查在 $max_retries 次重试后失败"
                log_error "请检查容器日志: docker-compose logs --tail=100 $service"
                return 1
            fi
        fi
    done

    return 1
}

# 清理未使用的镜像
cleanup() {
    log "清理未使用的镜像..."
    docker image prune -f
    log_success "清理完成"
}

# 获取要部署的服务列表
get_services_to_deploy() {
    if [ $# -eq 0 ]; then
        # 如果没有指定服务，返回默认服务列表
        echo "moer_overseas"
    else
        # 返回用户指定的服务
        echo "$@"
    fi
}

# 主函数
main() {
    log "===== 开始 MOER_OVERSEAS 服务器部署 ====="

    # 检查依赖
    check_dependencies

    # 拉取最新镜像
    pull_images

    # 获取要部署的服务列表
    local services_to_deploy=($(get_services_to_deploy "$@"))
    log "准备部署的服务: ${services_to_deploy[*]}"

    # 部署结果统计
    local successful_services=()
    local failed_services=()

    # 遍历每个服务，逐个部署和检查
    for service in "${services_to_deploy[@]}"; do
        log "========================================="
        log "开始处理服务: $service"

        # 部署单个服务
        if deploy_single_service "$service"; then
            log_success "服务 $service 部署成功"

            # 立即进行健康检查
            log "开始对服务 $service 进行健康检查..."
            if health_check "$service"; then
                log_success "服务 $service 健康检查通过"
                successful_services+=("$service")
            else
                log_error "服务 $service 健康检查失败"
                failed_services+=("$service")
            fi
        else
            log_error "服务 $service 部署失败"
            failed_services+=("$service")
        fi

        log "服务 $service 处理完成"
        echo ""
    done

    # 输出部署汇总
    log "========================================="
    log "部署汇总报告:"
    log "成功的服务 (${#successful_services[@]}): ${successful_services[*]}"
    if [ ${#failed_services[@]} -gt 0 ]; then
        log_error "失败的服务 (${#failed_services[@]}): ${failed_services[*]}"
    fi

    # 清理
    cleanup

    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "===== MOER_OVERSEAS 部署全部成功 ====="
        exit 0
    else
        log_error "===== MOER_OVERSEAS 部署部分失败 ====="
        exit 1
    fi
}

# 执行主函数，传递所有参数
main "$@"
