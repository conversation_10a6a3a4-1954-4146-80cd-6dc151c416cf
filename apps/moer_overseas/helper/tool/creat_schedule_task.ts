


/*
 * 按照是否是课程前一周、课程周、课程结束后的第几周，1-7 进行 + 时间 进行任务创建。
 */
import { IScheduleTime, DataService } from '../getter/get_data'
import { DateHelper } from 'lib/date/date'

export async function calTaskTime(time: IScheduleTime, chat_id: string, currentCourseNo?: boolean): Promise<Date> {
  if (time.day < 1 || time.day > 7) {
    throw new Error('Invalid day')
  }

  if (!/^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(time.time)) {
    throw new Error('Invalid time')
  }

  let courseStartDate: Date
  if (currentCourseNo) {
    courseStartDate = await DataService.getCourseStartTimeByCourseNo(DataService.getNextWeekCourseNo())
  } else {
    courseStartDate = await DataService.getCourseStartTime(chat_id)
  }

  courseStartDate.setHours(0, 0, 0, 0) // 将时间设置为当天的 00:00:00

  // 根据 IScheduleTime 的字段来调整基准日期
  const baseDate = new Date(courseStartDate)

  if (time.post_course_week !== undefined) {
    // 课程结束后的第几周
    baseDate.setDate(courseStartDate.getDate() + time.post_course_week * 7)
  } else if (time.is_course_week !== undefined) {
    if (time.is_course_week) {
      // 课程周，无需调整
    } else {
      // 课程前一周
      baseDate.setDate(courseStartDate.getDate() - 7)
    }
  } else {
    throw new Error('IScheduleTime 必须包含 is_course_week 或 post_course_week 字段')
  }

  // 计算目标日期
  const targetDate = new Date(baseDate)
  targetDate.setDate(baseDate.getDate() + time.day - 1)

  // 解析 time 字符串，并设置时间
  const [hours, minutes, seconds] = time.time.split(':').map(Number)
  targetDate.setHours(hours, minutes, seconds)

  return targetDate
}

export async function taskTimeToCourseTime(sendTime: Date | string, chat_id: string): Promise<IScheduleTime> {
  if (typeof sendTime === 'string') {
    sendTime = new Date(sendTime)
  }

  // 设置课程开始日期的时间为 00:00:00
  const courseStartDate = await DataService.getCourseStartTime(chat_id)
  const baseCourseDate = new Date(courseStartDate)
  baseCourseDate.setHours(0, 0, 0, 0)

  // 计算 sendTime 与课程开始日期的差值（天数）
  const timeDifference = sendTime.getTime() - baseCourseDate.getTime()
  const dayDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24)) // 将差值转换为天数

  const scheduleTime: IScheduleTime = {
    day: 1, // 默认值，将在后续逻辑中更新
    time: '00:00:00' // 默认值，将在后续逻辑中更新
  }

  if (dayDifference >= 0 && dayDifference < 7) {
    // 课程周
    scheduleTime.is_course_week = true
    scheduleTime.day = (dayDifference % 7) + 1
  } else if (dayDifference < 0 && dayDifference >= -8) {
    // 课程前一周
    scheduleTime.is_course_week = false
    scheduleTime.day = (dayDifference + 7) + 1 // 将天数调整为前一周的天数
  } else if (dayDifference >= 7) {
    // 课程结束后的第几周
    scheduleTime.post_course_week = Math.floor(dayDifference / 7)
    scheduleTime.day = (dayDifference % 7) + 1
  } else {
    throw new Error(`sendTime 超出了合理的范围（课程前一周、课程周或课程结束后的时间）dayDifference：${dayDifference}`)
  }

  // 格式化时间字符串为 'HH:mm:ss'
  const hours = sendTime.getHours().toString().padStart(2, '0')
  const minutes = sendTime.getMinutes().toString().padStart(2, '0')
  const seconds = sendTime.getSeconds().toString().padStart(2, '0')
  scheduleTime.time = `${hours}:${minutes}:${seconds}`

  return scheduleTime
}

/**
 * 返回 date1 是否 在 date 2 之前
 * @param date1
 * @param date2
 */
export function isScheduleTimeBefore(date1: IScheduleTime, date2: IScheduleTime): boolean {
  // 调用 isScheduleTimeAfter 函数，将参数顺序颠倒
  return isScheduleTimeAfter(date2, date1)
}

/**
 * 返回 date1 是否 在 date 2之后
 * @param date1
 * @param date2
 */
export function isScheduleTimeAfter(date1: IScheduleTime, date2: IScheduleTime): boolean {
  // 定义一个排序规则：
  // 如果存在 post_course_week，则返回它的值（默认 >= 1，表示上课周之后的周次）
  // 如果没有 post_course_week，则根据 is_course_week 判断：
  //   - 课程周（is_course_week 为 true）返回 0
  //   - 课程前一周（is_course_week 为 false）返回 -1
  function getWeekRank(schedule: IScheduleTime): number {
    if (typeof schedule.post_course_week === 'number') {
      // post_course_week 存在时，其值默认 >= 1，表示课程后一周、后两周等
      return schedule.post_course_week
    }
    if (typeof schedule.is_course_week === 'boolean') {
      // 课程周 > 课程前一周，因此：课程周返回 0，课程前一周返回 -1
      return schedule.is_course_week ? 0 : -1
    }
    // 如果都没提供，则默认当作课程周前一周
    return -1
  }

  const rank1 = getWeekRank(date1)
  const rank2 = getWeekRank(date2)

  // 如果不在同一周，直接比较周级别
  if (rank1 !== rank2) {
    return rank1 > rank2
  }

  // 同一周，则先比较 day（1～7，数字越大越靠后）
  if (date1.day !== date2.day) {
    return date1.day > date2.day
  }

  // 如果 day 也相同，则比较具体时间，利用 DateHelper.isTimeAfter 函数
  return DateHelper.isTimeAfter(date1.time, date2.time)
}