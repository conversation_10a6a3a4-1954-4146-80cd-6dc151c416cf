import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { PostAction } from './post_action'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { FreeTalk } from '../../workflow/freetalk'
import { chatStateStoreClient } from '../../service/instance'


export class AfterAdding extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const nodeCount = await chatStateStoreClient.getNodeCount(chatId, FreeTalk.name)

    return Promise.resolve(nodeCount <= 15)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterAdding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterAdding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}