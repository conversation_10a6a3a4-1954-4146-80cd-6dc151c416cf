import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { DataService } from '../../helper/getter/get_data'
import { MetaActions, ThinkPrompt } from '../meta_action'


export class AfterCourseWeek extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourseWeek = Boolean(currentTime.post_course_week) && currentTime.day != 1
    return Promise.resolve(afterCourseWeek)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourseWeek)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourseWeek)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

}