import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { DataService } from '../../helper/getter/get_data'
import { MetaActions, ThinkPrompt } from '../meta_action'


export class DuringCourse extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const duringCourse = await DataService.isWithinClassTime(currentTime)
    return Promise.resolve(duringCourse)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.duringCourse)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.duringCourse)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}