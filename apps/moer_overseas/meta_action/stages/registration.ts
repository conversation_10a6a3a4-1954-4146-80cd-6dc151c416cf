import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { DataService } from '../../helper/getter/get_data'
import { courseRegisterLink } from '../../service/global_data'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class Registration extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const isRegister = !await DataService.isRegisterCourse(chatId)
    return Promise.resolve(isRegister)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve(`\n课程注册链接：${courseRegisterLink}`)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.registration)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.registration)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}