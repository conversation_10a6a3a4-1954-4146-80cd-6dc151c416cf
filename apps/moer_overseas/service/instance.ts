import { EventTracker } from 'model/logger/data_driven'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { LLMReply } from 'service/llm/llm_reply'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { MemoryStore } from 'service/memory/memory_store'
import { SendMessageResultHandler } from 'service/message_handler/juzi/send_result_handler'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'
import { PrismaMongoClient } from '../helper/mongodb/prisma'
import { ChatDB } from '../database/chat_db'
import { ChatDB as ChatCommonDB } from 'service/database/chat'
import { Configuration, WhatsappMessagesApi, YCloudMessageSender } from 'service/message_handler/ycloud/message_sender'
import { RAGHelper } from 'service/rag/rag'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { YCloudCommonMessageSender } from 'service/visualized_sop/common_sender/ycloud'
import { MoerOverseasVisualizedSopProcessor } from '../visualized_sop/visualized_sop_processor'
import { FreeThink } from 'service/agent/freethink'
import { BaseHumanTransfer } from 'service/human_transfer/human_transfer'

const enterpriseName = 'moer_overseas'

const ycloudApiKey = 'b129ab33adb30bce3e9c0e77defd8011'

export const chatDBCommonClient = new ChatCommonDB(PrismaMongoCommonClient.newInstance(enterpriseName))
export const chatDBClient = new ChatDB(PrismaMongoClient.getInstance())
export const chatStateStoreClient = new ChatStateStore(chatDBCommonClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoCommonClient.newInstance(enterpriseName), chatStateStoreClient)
export const eventTrackClient = new EventTracker(PrismaMongoCommonClient.newInstance(enterpriseName))
export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const humanTransferClient = new BaseHumanTransfer(chatDBCommonClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBCommonClient, chatHistoryServiceClient)
export const sendMessageResultHandlerClient = new SendMessageResultHandler(chatHistoryServiceClient)
export const yCloudClient = new WhatsappMessagesApi(new Configuration({ apiKey: ycloudApiKey }))
export const yCloudMessageSender = new YCloudMessageSender(yCloudClient, chatHistoryServiceClient)
export const ragHelperClient = new RAGHelper(chatHistoryServiceClient)
export const moerOverseasExtractUserSlots = new ExtractUserSlots(chatHistoryServiceClient, chatStateStoreClient)
export const yCloudCommonMessageSender = new YCloudCommonMessageSender(yCloudMessageSender, chatHistoryServiceClient)
export const moerOverseasVisualizedSopProcessor = new MoerOverseasVisualizedSopProcessor('moer_overseas', chatDBCommonClient, chatHistoryServiceClient, yCloudCommonMessageSender)
export const freeThinkClient = new FreeThink(chatHistoryServiceClient, eventTrackClient)