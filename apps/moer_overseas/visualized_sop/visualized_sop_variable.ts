import { DataService } from '../helper/getter/get_data'
import { IChattingFlag } from '../state/user_flags'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient, moerOverseasExtractUserSlots } from '../service/instance'
import { PrismaMongoClient } from '../helper/mongodb/prisma'
import { UserLanguage } from '../helper/language/user_language_verify'

export const conditionJudgeMap:Record<string, ((params:{chatId:string;userId:string})=>Promise<boolean>)> = {
  '进群':async({ userId }) => {
    const isInGroup = await DataService.isInGroup(userId)
    return isInGroup
  },
  '先导课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:0 })
  },
  '第一节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:1 }) || DataService.isInClass(chatId, { day:1, is_recording:true })
  },
  '第二节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:2 }) || DataService.isInClass(chatId, { day:2, is_recording:true })
  },
  '第三节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:3 }) || DataService.isInClass(chatId, { day:3, is_recording:true })
  },
  '第四节课到课': async({ chatId }) => {
    return await DataService.isInClass(chatId, { day:4 }) || DataService.isInClass(chatId, { day:4, is_recording:true })
  },
  '先导课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:0 })
  },
  '第一节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:1 }) || DataService.isCompletedCourse(chatId, { day:1, is_recording:true })
  },
  '第二节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:2 }) || DataService.isCompletedCourse(chatId, { day:2, is_recording:true })
  },
  '第三节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:3 }) || DataService.isCompletedCourse(chatId, { day:3, is_recording:true })
  },
  '第四节课完课': async({ chatId }) => {
    return await DataService.isCompletedCourse(chatId, { day:4 }) || DataService.isCompletedCourse(chatId, { day:4, is_recording:true })
  },
  '先导课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:0 })
  },
  '第一节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:1 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:1, is_recording:true })
  },
  '第二节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:2 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:2, is_recording:true })
  },
  '第三节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:3 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:3, is_recording:true })
  },
  '第四节课看了一秒': async({ chatId }) => {
    return await DataService.isAttendCourseMoreThanOneSecond(chatId, { day:4 }) || DataService.isAttendCourseMoreThanOneSecond(chatId, { day:4, is_recording:true })
  },
  '完成四节课':async({ chatId }) => {
    return await DataService.isCompletedAllCourse(chatId)
  },
  '已下单': async({ chatId }) => {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return isPaid
  },
  '能量测评完成': async ({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return Boolean(state.is_complete_energy_test_analyze)
  },
  '财富果园解读': async({ chatId }) => {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    return Boolean(state.is_complete_wealth_orchard_analyze)
  },
  '对话过': async({ chatId }) => {
    const userMessageCount = await chatHistoryServiceClient.getUserMessageCount(chatId)
    return userMessageCount > 0
  },
  '对话过三轮以上': async ({ chatId }) => {
    const userMessageCount = await chatHistoryServiceClient.getUserMessageCount(chatId)
    return userMessageCount > 3
  },
  '时间到上课周': async({ chatId }) => {
    const time = await DataService.getCurrentTime(chatId)
    return Boolean(time.is_course_week)
  },
  '完成挖需': async({ chatId }) => {
    const userSlots = await moerOverseasExtractUserSlots.extractUserSlots(chatId, 6, { chat_id:chatId })
    return userSlots.isTopicSubTopicExist('基本信息', '生活角色') || userSlots.isTopicExist('过往冥想经验') || userSlots.isTopicExist('痛点') || userSlots.isTopicExist('冥想目标')
  },
  '有手机号': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const info = await mongoClient.chat.findFirst({ where:{ id:chatId }, select:{ chat_state:true } })
    if (info?.chat_state.userSlots?.['phoneNumber']) {
      return true
    }
    return false
  },
  '四节课都看完或者只看了第三节课或只看了第四节课': async({ chatId }) => {
    const day1Complete = await DataService.isCompletedCourse(chatId, { day:1 }) || await DataService.isCompletedCourse(chatId, { day:1, is_recording:true })
    const day2Complete = await DataService.isCompletedCourse(chatId, { day:2 }) || await DataService.isCompletedCourse(chatId, { day:2, is_recording:true })
    const day3Complete = await DataService.isCompletedCourse(chatId, { day:3 }) || await DataService.isCompletedCourse(chatId, { day:3, is_recording:true })
    const day4Complete = await DataService.isCompletedCourse(chatId, { day:4 }) || await DataService.isCompletedCourse(chatId, { day:4, is_recording:true })

    return (day1Complete && day2Complete && day3Complete && day4Complete) || (!day1Complete && !day2Complete && day3Complete && !day4Complete) || (!day1Complete && !day2Complete && !day3Complete && day4Complete)
  },
  '中文客户': async({ chatId }) => {
    const language = await UserLanguage.getLanguage(chatId)
    return language == UserLanguage.Language_ZH
  },
  '英语客户': async({ chatId }) => {
    const language = await UserLanguage.getLanguage(chatId)
    return language == UserLanguage.Language_EN
  }
}

export const textVariableMap:Record<string, (params:{chatId:string;userId:string})=> Promise<string>> = {
  '客户昵称': async({ chatId }) => {
    const chat = await chatDBClient.getById(chatId)
    if (!chat) {
      throw ('找不到这个客户')
    }
    return chat.contact.wx_name
  },
  '报名链接': async() => {
    return ''
  },
  '先导课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(0, chatId, false)
  },
  '第一节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(1, chatId, false)
  },
  '第一节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(1, chatId, true)
  },
  '第二节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(2, chatId, false)
  },
  '第二节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(2, chatId, true)
  },
  '第三节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(3, chatId, false)
  },
  '第三节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(3, chatId, true)
  },
  '第四节课上课链接':async({ chatId }) => {
    return await DataService.getCourseLink(4, chatId, false)
  },
  '第四节课录播链接':async({ chatId }) => {
    return await DataService.getCourseLink(4, chatId, true)
  },
  '手机号': async({ chatId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const info = await mongoClient.chat.findFirst({ where:{ id:chatId }, select:{ chat_state:true } })
    if (info?.chat_state.userSlots?.['phoneNumber']) {
      return info.chat_state.userSlots['phoneNumber'] as string
    }
    throw ('没有手机号')
  }
}

export const actionCustomMap:Record<string, (params:{chatId:string;userId:string})=> Promise<void>> = {
}

export const linkSourceVariableTagMap:Record<string, (params:{chatId:string;userId:string})=>Promise<string>> = {
}