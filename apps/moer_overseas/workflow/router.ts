import { Node } from './nodes/types'
import { getState, IWorkflowState } from 'service/llm/state'
import { WorkFlowNode } from './nodes/base_node'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { LLM } from 'lib/ai/llm/llm_model'
import { FreeTalk } from './freetalk'
import { RegexHelper } from 'lib/regex/regex'
import { getUserId } from 'config/chat_id'
import { ContextBuilder } from './context'
import { Reply } from './reply'
import { WealthOrchard } from './nodes/wealth_orchard'
import { EnergyTest } from './nodes/energy_test'
import { SendFileNode } from './nodes/send_file'
import { chatDBClient, chatHistoryServiceClient, humanTransferClient } from '../service/instance'
import { checkRobotDetection } from 'service/agent/utils'
import { chatStateStoreClient } from '../../yuhe/service/instance'
import { UserLanguage } from '../helper/language/user_language_verify'
import { getPrompt } from 'service/agent/prompt'
import logger from 'model/logger/logger'

export const NodeMap: { [key in Node]?: typeof WorkFlowNode } = {
  [Node.FreeTalk]: FreeTalk,
  [Node.WealthOrchard]: WealthOrchard,
  [Node.EnergyTest]: EnergyTest,
  [Node.SendFile]: SendFileNode
}

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<Node> {
    const chatId = state.chat_id
    const userId = state.user_id
    const roundId = state.round_id
    const userMessage = state.userMessage
    if (!userMessage) return Node.Dummy

    // 检查是否为回复问卷
    // const isRepliedSurvey = await this.checkIsRepliedSurvey(state.chat_id, state.userMessage)
    // if (isRepliedSurvey) {
    //   return Node.DummyEnd
    // }

    // 废话内容过滤
    const isChatter = RegexHelper.filterChatter(userMessage)
    const beforeCourse3 = true
    if (isChatter && beforeCourse3) return Node.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chatId, roundId, userId, userMessage)
    if (isRobotDetection) return Node.DummyEnd

    // 意图分类路由
    return await this.routeByCategory(userMessage, chatId, userId, roundId)
  }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, user_id: string, round_id: string): Promise<Node> {
    const category = await Router.classify(userMessage, chat_id, round_id)

    if (category === 1) { //能量测评
      return Node.EnergyTest
    } else if (category === 2) { //财富果园
      return Node.WealthOrchard
    } else if (category === 3) { //发送课程资料
      return Node.SendFile
    } else if (category === 4) { //看课问题
      const phoneNumber = await chatDBClient.getPhoneNumber(chat_id)
      const email = await chatDBClient.getEmail(chat_id)
      if (!phoneNumber || !email) {
        return Node.FreeTalk
      }

      const languageOption = await UserLanguage.getLanguageSetting(chat_id)
      //截取手机号后6位
      const phoneNumberLast6 = phoneNumber.substring(phoneNumber.length - 6)
      const state = await getState(chat_id, user_id, '')
      state.round_id = round_id
      const context = await ContextBuilder.build({
        state: state,
        includeRAG: true,
        talkStrategyPrompt:`客户当前看课碰到问题，请你协助客户
 如果客户无法登陆直播间看课，可以告诉客户可以使用${email}登陆，然后默认密码是手机号后六位：${phoneNumberLast6}\n${languageOption}`
      })
      await Reply.invoke({
        state: state,
        context: context,
        promptName:'send_phone_number'
      })
      return Node.DummyEnd
    } else if (category === 5) { //异常问题处理
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    }
    return Node.Dummy
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('router')
    const routingNodes = `1. 能量测评：此节点涉及能量测试。客户应该点击能量测试链接，完成测试后会有一个分数。一般客户会有对这个分值的疑惑，前面我们也允诺对这个测评结果进行解读。只有当客户明确提到能量测评或能量分数时，才将输入分类到此节点
  - 例如："老师，我测完了，328分", "60分，怎么办，老师可以帮忙看看怎么回事吗？", "435分，老师可以帮忙解读下吗？"

2. 财富果园：客户输入为财富果园的景象，景象包括果园里树木和大门，围栏，秋天的动作，四季的变换循环景色等。只有当客户明确提到他们想象的果园中的具体画面时，才将输入分类到此节点，反之则不属于这个节点
  - 例如："没有大门，小冠木围栏，主树不清晰，没有看到结果，四季常青", "灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了"
  - 注意：如果客户只让解读，没有给画面，则不属于这个节点

3. 发送课程资料：客户输入为【发送资料/奖励/完课礼/课程回放请求/音频/视频/思维导图/课程笔记/海浪冥想/身心对照表/沉浸式秒睡/α波纯音乐/红靴子音频/路易斯/小课堂链接】或其他冥想相关资料时，判断前请不要进行过度推理
  - 例如：“请问有课程回放吗”“有回放吗”“要回看”“能否补听”“【图片】完成第二课后获得的奖励”“老师说给一个礼物是什么”“领表”“现在没法看回放吗？”“对照表是什么？”“视频里说有一个表格”“身心对照卡点表何时给我”
  - 注意：若客户询问“回放有效期”，或者说“已看完回放”，则不是索要回放行为，不属于这个节点，其他资料同理，客户索要“直播间链接”也不属于这个节点

4. 看课问题：客户表示无法进入直播，无权限观看，无法看录播等看课相关问题时进入此节点
  - 注意：网络问题，卡顿等跟看课无关问题不属于这个节点

5. 异常问题处理：当客户表示投诉，退款，快递查询，更换课程时间或更换导师需求时进入此节点
  - 例如：投诉（你们的产品是骗人的，我要投诉），退款（我要退款/退费/退课），快递查询（我的垫子到哪了），时间变更（我想上下一期课程），导师变更（我要换助教，能换个助教吗）`
    const output = await LLM.predict(
      routerPrompt, {
        response_json: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        routingNodes: routingNodes,
        customerMessage: userMessage,
      })
    let answer: number = 0

    try {
      const parsedOutput = JSON.parse(output)
      answer = parsedOutput.answer
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    return answer || 0
  }

  public static async checkIsRepliedSurvey(chat_id: string, userMessage: string) {
    const isRepliedSurvey = await this.isRepliedSurvey(userMessage)
    if (!isRepliedSurvey) { return null }

    const userMessageCount = await chatHistoryServiceClient.getUserMessageCount(chat_id)
    if (userMessageCount > 9) { return null }

    const dynamicPrompt = `- 根据学员选择的数字组合（例如：1-5-8），结合这些信息建立的是一种温柔引导、具有共情力又不具压迫感的引导式简短回复，让学员感觉被理解和支持，而不是被盘问
- 你将围绕三个模块（生活角色、冥想经验、人生议题）设计对话引导一个问题。这个问题可以作为你了解学员更深层状态的温和提问
这种回应最好结构是：
- 共情与理解：确认对方的状态但不要重复客户的选择
- 感情上引导性追问，把学员提到的第三点数字组合更深入得拆解一层，需要有边界感`

    const context = await ContextBuilder.build({
      state: await getState(chat_id, getUserId(chat_id), userMessage),
      includeRAG: true,
      talkStrategyPrompt: dynamicPrompt,
    })

    await Reply.invoke({
      state: await getState(chat_id, getUserId(chat_id), userMessage),
      context: context,
      promptName: 'reply_survey',
    })
    return Node.DummyEnd
  }

  private static async isRepliedSurvey(userMessage: string) {
    // 检查是否是手机号
    if (RegexHelper.extractPhoneNumber(userMessage)) {
      return false
    }

    /** 把常用中文数字映射成阿拉伯数字 */
    const chineseMap: Record<string, number> = {
      '一': 1,  '二': 2,  '三': 3,  '四': 4,
      '五': 5,  '六': 6,  '七': 7,  '八': 8,  '九': 9,
      '十': 10, '十一': 11, '十二': 12, '十三': 13,
    }

    const numbers: number[] = []

    /* 1️⃣ 抓中文数字——先匹配长度较长的“十一”“十二”“十三”，再匹配其余 */
    const cnRegex = /十一|十二|十三|十|[一二三四五六七八九]/g;
    (userMessage.match(cnRegex) || []).forEach((cn) => numbers.push(chineseMap[cn]))

    /* 2️⃣ 抓阿拉伯数字 1–13——用重叠匹配把 8910… 拆成 8 9 10… */
    const arRegex = /1[0-3]|[1-9]/g;
    (userMessage.match(arRegex) || []).forEach((num) => numbers.push(parseInt(num, 10)))

    /* 3️⃣ 判断三大区间是否各出现至少一次 */
    const hasRole   = numbers.some((n) => n >= 1 && n <= 4)   // 生活角色
    const hasExp    = numbers.some((n) => n >= 5 && n <= 7)   // 冥想经验
    const hasTopic  = numbers.some((n) => n >= 8 && n <= 13)  // 人生议题

    return (hasRole ? 1 : 0) + (hasExp ? 1 : 0) + (hasTopic ? 1 : 0) >= 2
  }
}