import { IWorkflowState } from 'service/llm/state'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { TaskRegister } from '../../schedule/task_register'
import { DataService } from '../../helper/getter/get_data'
import { FreeTalk } from '../freetalk'
import { UUID } from 'lib/uuid/uuid'
import { Config } from 'config'
import { PrismaMongoClient } from 'model/mongodb/prisma'
import { chatHistoryServiceClient } from '../../service/instance'

describe('Test', function () {
  beforeAll(() => {

  })

  it('delete user', async () => {
    Config.setting.projectName = 'moer_overseas'
    const chatId = '+8617326651677_+85297257364'
    await chatHistoryServiceClient.clearChatHistory(chatId, false)
    await PrismaMongoClient.getInstance().log_store.deleteMany({ where: { chat_id: chatId } })
    await PrismaMongoClient.getInstance().chat.deleteMany({ where: { id: chatId } })
  }, 60000)


  it('freeTalkTest', async () => {
    const chatId = '7881300516060552_1688857404698934'
    await mockUserData(chatId)
    const userMessage = '泥嚎'
    await chatHistoryServiceClient.addUserMessage(chatId, userMessage)
    const dbMessages = await chatHistoryServiceClient.getChatHistoryByChatId(chatId)

    const state: IWorkflowState = {
      chat_id: chatId,
      user_id: UUID.v4(),
      round_id: UUID.v4(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chatId),
    }

    TaskRegister.register()
    DataService.getCurrentTime = async () => {
      return {
        day: 1,
        time: '16:00:00',
      }
    }
    DataService.isPaidSystemCourse = async () => {
      return false
    }

    await FreeTalk.invoke(state)
  }, 9E8)
})

export async function mockUserData(chatId: string) {
  await mockChatHistory(chatId)
}

async function mockChatHistory(chatId: string) {
  const fullChatHistory = await chatHistoryServiceClient.getChatHistoryByChatId(chatId)
  chatHistoryServiceClient.getChatHistoryByChatId = async (chat_id: string) => {
    return fullChatHistory
  }
}