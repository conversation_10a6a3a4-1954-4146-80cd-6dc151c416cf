import { BaseWorkFlow } from 'service/workflow/workflow'
import { getState, IWorkflowState } from 'service/llm/state'
import { AsyncLock } from 'model/lock/lock'
import logger from 'model/logger/logger'
import { Node } from './nodes/types'
import { NodeMap, Router } from './router'
import { Config } from 'config'
import {
  chatHistoryServiceClient,
  chatStateStoreClient,
  memoryStoreClient,
  moerOverseasExtractUserSlots
} from '../service/instance'
import { UserLanguage } from '../helper/language/user_language_verify'
import { GroupNotification } from 'service/group_notification/group_notification'


export class WorkFlow extends BaseWorkFlow {
  /**
   * 对话流程
   * @param chat_id
   * @param user_id
   * @param userMessage
   */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    userMessage = this.transferWechatEmoji(userMessage)
    logger.debug('step start')
    await chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
    if (userMessage === '[表情]') {
      return // 表情消息 暂不做处理
    }

    if (userMessage.toLowerCase().includes('clear') && Config.isLocalTestAccount()) {
      await this.resetChat(chat_id, user_id)
      return
    }

    const entryNode = (await chatStateStoreClient.get(chat_id)).nextStage

    const lock = new AsyncLock()

    const state = await getState(chat_id, user_id, userMessage) // 如果有新消息，在锁释放后，当前流程会中断

    await lock.acquire(chat_id, async () => { // 如果有新消息，当前回复会被丢弃
      await WorkFlow.run(entryNode as Node, state)
    }, { timeout: 2 * 60 * 1000 })
  }

  private static async run (entryNode: Node, state: IWorkflowState) {
    let node = NodeMap[entryNode]
    logger.trace({ chat_id: state.chat_id }, `初始跳转节点: ${entryNode}`)
    if (!node) {
      logger.error(`[MoerOverseasFlow] node not found: ${entryNode}`)
      return
    }

    await this.preReply(state)

    // 根据客户消息自动转移
    const autoTransferNode = await Router.route(state)
    if (autoTransferNode === Node.DummyEnd) {
      return
    }

    if (autoTransferNode && autoTransferNode !== Node.Dummy) {
      logger.trace({ chat_id: state.chat_id }, `重定向到节点: ${autoTransferNode}`)

      node = NodeMap[autoTransferNode]
      if (!node) {
        logger.error(`[MoerOverseasFlow] auto transfer node not found: ${autoTransferNode}`)
        return
      }
    }

    //todo 逻辑是否保留待确认

    // if (autoTransferNode !== Node.Homework1 && autoTransferNode !== Node.Homework2) { // 作业未处理完，将消息进行延迟处理，等待作业处理结束后处理
    //   if (Homework1Store.getUserMessages(state.chat_id).length > 0 || Homework2Store.getUserMessages(state.chat_id).length > 0) {
    //     HomeworkDurationUserMessageStore.addUserMessage(state.chat_id, state.userMessage)
    //     await sleep(3 * 60 * 1000) // 1分钟后再进行处理
    //     // 把当前消息挪到最后作为新消息处理，防止受到作业的影响
    //     const userMessages = HomeworkDurationUserMessageStore.getUserMessages(state.chat_id)
    //     if (userMessages.length > 0 && userMessages[userMessages.length - 1] != state.userMessage) {
    //       return
    //     }
    //     HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
    //     setTimeout(() => {
    //       HomeworkDurationUserMessageStore.clearUserMessages(state.chat_id)
    //     }, 1000 * 60 * 3)
    //     for (const message of userMessages) {
    //       await chatHistoryServiceClient.moveToEnd(state.chat_id, message)
    //     }
    //
    //   }
    // }

    logger.debug(`invoke ${node}`)
    const nextStage = await node.invoke(state)
    await chatStateStoreClient.update(state.chat_id, { nextStage })

    await this.postReply(state)
  }

  //todo 待补全
  private static async resetChat(chat_id: string, user_id: string) {

  }

  private static async preReply(state: IWorkflowState) {
    // 异步提取 Memory, 客户槽位
    memoryStoreClient.pushRecentMemoryToVectorDB(state.chat_id, state.round_id, moerOverseasExtractUserSlots)
    // 识别客户语言信息，传入当前用户消息用于语言切换检测
    UserLanguage.verify(state.chat_id, state.userMessage)
  }

  private static async postReply(state: IWorkflowState) {
  }

  public static async humanInvolveGroupNotify(contactName: string, courseNo: number | null, userMessage: string) {
    const imBotId = '1688858254705213'
    const groupId = Config.setting.wechatConfig?.notifyGroupId
    await GroupNotification.notify(`${contactName}（${courseNo}）客户AI未开启，请人工处理\n客户：${userMessage}`, groupId, imBotId)
  }



  private static transferWechatEmoji(message: string) {
    const emojiRegex = /\[.*?\]/g
    const emojiMap = {
      '[微笑]':'😊',
      '[调皮]': '😝',
      '[合十]': '🙏',
      '[爱心]': '💗',
      '[玫瑰]': '🌹',
      '[捂脸]': '🤦',
      '[笑哭]': ' 😂',
      '[咖啡]': '☕️',
      '[抱拳]': '🙏',
      '[拥抱]':'🫂'
    }
    return message.replace(emojiRegex, (match) => {
      const emoji = emojiMap[match]
      return emoji || match
    })
  }
}