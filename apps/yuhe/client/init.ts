import { loadConfigByAccountName } from 'service/database/config'
import chalk from 'chalk'
import { Config } from 'config'
import { TaskRegister } from '../schedule/register_task'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { yuheVisualizedGroupSopProcessor, yuheVisualizedSopProcessor } from '../service/instance'

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  // 注入配置
  // 读取注入的 姓名
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  Config.setting.wechatConfig = await loadConfigByAccountName(name)
  Config.setting.startTime = Date.now()
  Config.setting.BOT_NAME = (['yuhe4', 'yuhe5', 'yuhe6', 'yuhe11', 'yuhe14', 'yuhe16', 'yuhe18', 'yuhe21', 'yuhe22', 'yuhe31', 'yuhe36'].includes(name)) ? '多米老师' : '大麦老师'
  Config.setting.projectName = 'yuhe'

  console.log(
    chalk.green(
      `当前账号：${chalk.bold(Config.setting.wechatConfig?.name)}`,
    ),
  )

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  // SOP Worker
  yuheVisualizedSopProcessor.start()
  yuheVisualizedGroupSopProcessor.start()

  // Config.setting.onlyReceiveMessage = true
}