import { LLM } from 'lib/ai/llm/llm_model'
import logger from 'model/logger/logger'
import { IRoomMessage, XingyanAPI } from 'model/xingyan'
import { DataService } from '../helper/getter/get_data'
import pLimit from 'p-limit'
import dayjs from 'dayjs'
import customParseFormat from 'dayjs/plugin/customParseFormat'
import { XMLHelper } from 'lib/xml/xml'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { ChatHistoryWithRoleAndDate } from 'service/user_slots/extract_user_slots'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { GroupNotification } from 'service/group_notification/group_notification'
import { LRUCache } from 'lru-cache'
import { chatDBClient } from '../service/instance'
import { YuHeAccountType, Config } from 'config'

dayjs.extend(customParseFormat)

export interface QA {
  question: string;  // 讲师的互动文本
  answer: string;    // 弹幕回答
  timestamp: number; // 互动发生时间
}

// 课程时间段枚举
export enum CourseTimeSlot {
  MORNING = 'morning',   // 上午课程 (14:00)
  EVENING = 'evening'    // 晚上课程 (18:50)
}

const TIME_MATCH_THRESHOLD = 120 // 定义弹幕时间与问题时间匹配的阈值（秒）
const PURCHASE_KEYWORDS = ['付', '支付', '买', '钱', '价格', '优惠', '报名', '学费', '下单', '加入'] // 购买意向关键词

export class DanmuAnalyzer {
  // 添加静态 LRUCache 实例，用于存储已处理过的 roomId
  private static readonly processedRoomCache = new LRUCache<string, boolean>({ max: 10 })

  private static readonly PURCHASE_INTENT_QUESTIONS_A = { // 存储特定时间点的购买意向问题
    2: [
      { time: 117, expectedAnswer: '6', question: '确定要买的评论区回复6' }
    ],
    3: [
      { time: 89, expectedAnswer: '1', question: '确认要加入的在评论区回复1' },
      { time: 110, expectedAnswer: '愿意', question: '未来的一年愿意跟着中神通团队一起做流量获客的回复愿意' }
    ],
    4: [
      { time: 75, expectedAnswer: '1', question: '确定要交这个钱，要学的评论区回复1' }
    ]
  }
  private static readonly PURCHASE_INTENT_QUESTIONS_B = { // 存储特定时间点的购买意向问题
    2: [
      { time: 103, expectedAnswer: '有', question: '对咱们这个陪跑感兴趣的回复有' },
      { time: 113, expectedAnswer: '想', question: '想让专业团队带着你、陪着你做抖音的回复想' }
    ],
    3: [
      { time: 75, expectedAnswer: '1', question: '确定要交这个钱，要学的评论区回复1' },
      { time: 172, expectedAnswer: '要', question: '2025年要不要跟着我们团队一起打个翻身仗？' },
      { time: 218, expectedAnswer: '愿意', question: '各位想不想跟我们一起？愿意跟我们一起的回复愿意' }
    ],
    4: [
      { time: 86, expectedAnswer: '1', question: '确定要交这个钱，要学的评论区回复1' }
    ]

  }

  private static readonly QUESTIONS_BY_DAY_A = { // 静态常量，存储不同天数的问题列表
    1: [
      { time: 11, question: '你们做抖音有多久了？没做过的回复0，1年的回复1，2年的回复2，3年的回复3' },
      { time: 20, question: '经营时是否有做过团购？做过的回复有，没做过的回复没有' },
      { time: 49, question: '你有没有研究过你的同行是怎么赚钱的？有研究过的回复有，没研究过的回复没有' },
      { time: 51, question: '你现在是个人号还是企业号？' },
      { time: 59, question: '你的产品是标品吗？' },
      { time: 116, question: '你现在对于自身产品定位（商业定位）和视频的表现形式（内容定位）有没有基本的感觉和了解？' },
    ],
    2: [
      { time: 17, question: '你拍的短视频没有流量的回复1，有流量但是没有咨询，没有变现成客源的回复2' },
      { time: 100, question: '直播间的人有没有找过抖音账号代运营的？有的回复有' },
      { time: 143, question: '直播间有没有不在北上广深的？有没有非一线城市的？有的回复有' },
      { time: 168, question: '大家有没有投过广告？投过广告的回复1，没投过的回复2' },
      { time: 225, question: '直播间有没有年纪大的？有的回复有' },
    ],
    3: [
      { time: 40, question: '各位现在市面上比较主流的5个短视频平台都下载了的回复有' },
      { time: 100, question: '直播间各位有没有卖高客单价产品的？有的回复有' },
      { time: 102, question: '直播间各位有没有卖低客单价产品的？有的回复有' },
      { time: 112, question: '大家有没有做过直播？做过的回复1，没做过的回复2' }
    ],
    4: [
      { time: 151, question: '有没有年纪稍微大一点的老板，40岁以上的，有没有？' },
      { time: 176, question: '直播间的各位开实体门店的回复1' },
      { time: 177, question: '直播间的各位开工厂的回复2' }
    ]
  }
  private static readonly QUESTIONS_BY_DAY_B = { // 静态常量，存储不同天数的问题列表
    1: [
      { time: 12, question: '你们做抖音有多久了？没做过的回复0，1年的回复1，2年的回复2，3年的回复3' },
      { time: 23, question: '经营时是否有做过团购？做过的回复有，没做过的回复没有' },
      { time: 29, question: '自己平时会用AI的回复有，不会用或者没用过的回复没有' },
      { time: 48, question: '你有没有研究过你的同行是怎么赚钱的？有研究过的回复有，没研究过的回复没有' },
      { time: 50, question: '你现在是个人号还是企业号？' },
      { time: 60, question: '你的产品是标品吗？' },
    ],
    2: [
      { time: 7, question: '你的抖音账号已经给你带来收益的回复有，没有的回复没有' },
      { time: 86, question: '有没有用过DeepSeek的？用过的回复有，没用过的回复没有' },
      { time: 155, question: '有没有不在北上广深的？有没有非一线城市的？有的回复有' },
      { time: 180, question: '大家有没有投过广告？投过广告的回复有，没投过的回复没有' },
    ],
    3: [
      { time: 110, question: '平均客单价超过500元的回复有' },
      { time: 124, question: '做过直播的回复1，没有做过的回复2' },
    ],
    4: [
      { time: 4, question: '做私域运营的老板回复有' },
    ]
  }

  private purchaseIntentTemplate: string = `## 弹幕分析
你需要分析一条视频中的评论，该视频中讲师教授观众如何使用各种AI工具在抖音（中国版TikTok）上制作短视频以吸引潜在客户。在视频中，讲师还推广自己的后续课程

你的任务是确定这条评论是否明确表达了购买意向。购买意向定义为以下情况：
明确表示有兴趣购买的行为，例如：
   - 询问课程价格
   - 询问如何购买或报名课程
   - 请求报名信息
   - 询问支付方式
   - 表达直接购买的意愿

视频中的评论及其时间戳的格式为：
弹幕内容：{content}
弹幕时间戳：{timestamp}

请分析此评论，判断它是否表现出明确的购买意向。

<thinking>
首先，我将仔细检查评论文本，寻找表明购买意向的关键字或短语：
1. 评论是否询问价格？（例如，"多少钱"、"价格"、"费用"）
2. 是否询问如何购买？（例如，"怎么买"、"如何购买"、"在哪里买"）
3. 是否询问报名信息？（例如，"怎么报名"、"如何加入"、"如何参与"）
4. 是否提到支付方式？（例如，"支付方式"、"微信支付"、"支付宝"）
5. 是否表达直接购买的意愿？（例如，"我想买"、"我要购买"、"我想参加"）

如果评论包含以上任何指标，则表示存在购买意向。如果只是表达赞赏、询问视频内容或发表一般性评论，则不视为购买意向。
</thinking>

根据你的分析，在以下XML格式中提供判断结果：
<isPurchaseIntent>true</isPurchaseIntent> 或 <isPurchaseIntent>false</isPurchaseIntent>

示例：
1. 评论："这个课程多少钱？"（How much does this course cost?）
   结果：<isPurchaseIntent>true</isPurchaseIntent>

2. 评论："怎么报名参加你的课程？"（How do I sign up for your course?）
   结果：<isPurchaseIntent>true</isPurchaseIntent>

3. 评论："讲得真好，学到很多！"（Great explanation, I learned a lot!）
   结果：<isPurchaseIntent>false</isPurchaseIntent>

4. 评论："AI工具在哪里下载？"（Where can I download the AI tool?）
   结果：<isPurchaseIntent>false</isPurchaseIntent>`

  // 经过关键字匹配之后的兜底判断
  public async isPurchaseIntent(danmuMessage: IRoomMessage, day: number): Promise<{ isPurchaseIntent: boolean } | null> {

    const purchaseTemplate = SystemMessagePromptTemplate.fromTemplate(this.purchaseIntentTemplate)
    const response = await LLM.predict(purchaseTemplate, {}, {
      content: danmuMessage.content,
      timestamp: danmuMessage.msgTime,
      day: day
    })

    const content = XMLHelper.extractContent(response, 'isPurchaseIntent')

    if (content === null) {
      return null
    }
    const isPurchaseIntent = content.trim().toLocaleLowerCase() === 'true'
    return { isPurchaseIntent }
  }

  // 处理购买意向 （特定时间点回复 + 关键字匹配 + isPurchaseIntent兜底判断）
  private async handlePurchaseIntentDanmu(danmu: IRoomMessage[], chatId: string, day: number, timeSlot: CourseTimeSlot, courseStartTime: number, accountType: YuHeAccountType) {
    let purchaseIntentCount = 0

    for (const msg of danmu) {
      // 首先检查是否是特定时间点的特定回复
      const msgTimeSeconds = this.convertMsgTimeToSeconds(msg.msgTime)
      const msgDate = dayjs(msg.msgTime).format('YYYY-MM-DD')
      const minutesFromStart = Math.floor((msgTimeSeconds - courseStartTime) / 60)

      const dayQuestions = (accountType === YuHeAccountType.ZhongShenTong
        ? DanmuAnalyzer.PURCHASE_INTENT_QUESTIONS_A
        : DanmuAnalyzer.PURCHASE_INTENT_QUESTIONS_B)[day] || []
      let isSpecificTimeReply = false
      for (const q of dayQuestions) {
        if (Math.abs(minutesFromStart - q.time) <= 2 && msg.content.trim() === q.expectedAnswer) {
          isSpecificTimeReply = true
          logger.log(`[DanmuAnalyzer] 发现特定时间点购买意向回复: ${msg.content} (时间段: ${timeSlot})`)
          // 如果确认是特定时间点的购买意向回复，进行处理
          const qa: QA = {
            question: q.question,
            answer: msg.content,
            timestamp: msgTimeSeconds,
          }
          await this.sendPaymentHelp(qa, chatId)
          purchaseIntentCount++
          break
        }
      }

      if (isSpecificTimeReply) {
        continue // 如果已经处理了特定时间点的回复，跳过后续处理
      }

      // 检查弹幕内容是否包含任何购买关键词（不区分大小写）
      const hasKeyword = PURCHASE_KEYWORDS.some((keyword) => msg.content.toLowerCase().includes(keyword.toLowerCase()))

      if (hasKeyword) {
        const isPurchaseIntent = await this.isPurchaseIntent(msg, day) // 如果找到关键字就交给兜底逻辑判断

        if (isPurchaseIntent?.isPurchaseIntent) {
          // 如果确认是购买意向，进行处理
          // 为 sendPaymentHelp 创建一个虚拟的问答对
          const qa: QA = {
            question: '购买意向',
            answer: msg.content,
            timestamp: msgTimeSeconds,
          }
          await this.sendPaymentHelp(qa, chatId)
          purchaseIntentCount++
        }
      }
    }
  }


  private async sendPaymentHelp(qa: QA, chatId: string) {
    try {
      let contactName = chatId
      const chat = await chatDBClient.getById(chatId)
      if (chat?.contact?.wx_name) contactName = chat.contact.wx_name

      const notifyMsg = `弹幕识别 ${contactName} 可能有购买意向\n问题：${qa.question}\n回答：${qa.answer}`

      // ✅ 直接群通知，无需真正转人工
      await GroupNotification.notifyToHumanGroup(notifyMsg)
    } catch (error) {
      logger.trace(`发现弹幕购买意向 ${chatId}: ${qa.question} - ${qa.answer}, 但群通知失败: ${error}`)
    }
  }

  private convertMsgTimeToSeconds(msgTime: string): number {
    // 将消息时间字符串（格式："YYYY-MM-DD HH:mm:ss"）转换为秒数
    const date = dayjs(msgTime, 'YYYY-MM-DD HH:mm:ss')
    if (!date.isValid()) {
      logger.trace(`无效的消息时间格式: ${msgTime}`)
      return NaN // 或者适当处理错误
    }
    return date.unix() // 获取 Unix 时间戳
  }

  extractUserSlotTopicRecommendation = `- 基础信息：年龄、性别、城市、职业
- 技术水平或对AI工具的熟悉程度
- 产品定位
- 所属行业和市场细分
- 面临的挑战和痛点
- 内容创作偏好
- 使用抖音/社交媒体的经验水平`

  private async processUser(liveId: string, phone: string, chatId: string, day: number, xingyanAPI: XingyanAPI, accountType: YuHeAccountType): Promise<void> {
    try {
      const danmu = await xingyanAPI.getRoomMsgPage(liveId, phone)

      if (!danmu || danmu.length === 0) {
        logger.warn(`[DanmuAnalyzer] 客户 ${phone} 没有弹幕记录`)
        return
      }

      // 提前获取客户的课程时间段和开始时间，避免重复计算
      const timeSlot = this.getCourseTimeSlot(danmu)
      const msgDate = danmu.length > 0 ? dayjs(danmu[0].msgTime).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')
      const courseStartTime = this.getCourseStartTime(msgDate, timeSlot)

      logger.log(`[DanmuAnalyzer] 客户 ${phone} 课程时间段: ${timeSlot}, 开始时间: ${dayjs.unix(courseStartTime).format('YYYY-MM-DD HH:mm:ss')}`)

      await this.handlePurchaseIntentDanmu(danmu, chatId, day, timeSlot, courseStartTime, accountType)
      const chatHistory: ChatHistoryWithRoleAndDate[] = []

      for (const msg of danmu) {
        const date = msg.msgTime
        // 找到最接近的问题，直接传入已计算的课程开始时间
        const question = this.findClosestQuestion(msg.msgTime, day, courseStartTime, accountType)
        if (question) {
          chatHistory.push(
            {
              role: 'assistant',
              date: date,
              message: question
            },
            {
              role: 'user',
              date: date,
              message: msg.content
            }
          )
        }
      }

      if (chatHistory.length > 0) {
        await new ExtractUserSlots().extractUserSlotsFromChatHistory({
          chatId,
          chatHistory,
          logInfo: { chat_id: chatId },
          topicRecommendations: this.extractUserSlotTopicRecommendation,
          topicRules: ''
        })
      }
    } catch (error) {
      logger.warn(`[DanmuAnalyzer] 处理客户 ${phone} 弹幕失败:`, error)
    }
  }

  private findClosestQuestion(msgTime: string, day: number, courseStartTime: number, accountType: YuHeAccountType): string | null {
    const msgTimeSeconds = this.convertMsgTimeToSeconds(msgTime)
    let closestQuestion = null
    let minTimeDiff = Infinity

    logger.log(`[DanmuAnalyzer] 时间匹配: 消息时间=${msgTime}, 课程开始时间=${dayjs.unix(courseStartTime).format('YYYY-MM-DD HH:mm:ss')}`)

    // 只检查指定天的问题
    const dayQuestions = (accountType === YuHeAccountType.ZhongShenTong
      ? DanmuAnalyzer.QUESTIONS_BY_DAY_A
      : DanmuAnalyzer.PURCHASE_INTENT_QUESTIONS_B)[day] || []
    for (const q of dayQuestions) {
      if (!q.time || !q.question) continue

      // 将分钟转换为秒（time是相对于课程开始时间的分钟数）
      const questionTimeInSeconds = q.time * 60
      // 计算问题实际发生的时间（课程开始时间 + 问题相对时间）
      const questionTime = courseStartTime + questionTimeInSeconds
      const timeDiff = Math.abs(msgTimeSeconds - questionTime)

      if (timeDiff < minTimeDiff && timeDiff <= TIME_MATCH_THRESHOLD) {
        minTimeDiff = timeDiff
        closestQuestion = q.question
        logger.log(`[DanmuAnalyzer] 找到匹配问题: 问题时间=${dayjs.unix(questionTime).format('HH:mm:ss')}, 时间差=${timeDiff}秒, 问题="${q.question}"`)
      }
    }

    return closestQuestion
  }

  /**
   * 获取客户的课程时间段
   * 根据直播间结束时间来判断是上午课程还是晚上课程
   * 弹幕分析器只在直播间结束时触发，所以当前时间就是直播间结束时间
   * @param danmu 客户的弹幕消息（保留参数以保持接口兼容性）
   * @returns 课程时间段
   */
  private getCourseTimeSlot(danmu: IRoomMessage[]): CourseTimeSlot {
    // 弹幕分析器只在直播间结束时触发，当前时间就是直播间结束时间
    const currentHour = dayjs().hour()

    // 上午课程时间段：12:00-18:00 (包含14:00课程)
    if (currentHour >= 12 && currentHour < 18) {
      logger.log(`[DanmuAnalyzer] 根据直播间结束时间判断为上午课程: ${currentHour}:00`)
      return CourseTimeSlot.MORNING
    }

    // 晚上课程时间段：18:00-24:00 (包含18:50课程)
    if (currentHour >= 18 && currentHour < 24) {
      logger.log(`[DanmuAnalyzer] 根据直播间结束时间判断为晚上课程: ${currentHour}:50`)
      return CourseTimeSlot.EVENING
    }

    // 其他时间段（如凌晨）默认返回晚上课程（保持原有行为）
    logger.log(`[DanmuAnalyzer] 直播间结束时间不在课程时间段内: ${currentHour}:00，默认返回晚上课程`)
    return CourseTimeSlot.EVENING
  }

  /**
   * 根据课程时间段获取课程开始时间
   * @param msgDate 消息日期
   * @param timeSlot 课程时间段
   * @returns 课程开始时间的Unix时间戳
   */
  private getCourseStartTime(msgDate: string, timeSlot: CourseTimeSlot): number {
    if (timeSlot === CourseTimeSlot.MORNING) {
      // 上午课程：14:00
      return dayjs(`${msgDate} 14:00:00`).unix()
    } else {
      // 晚上课程：18:50
      return dayjs(`${msgDate} 18:50:00`).unix()
    }
  }

  /**
   * 基于 roomId 分析直播间内的客户弹幕
   * @param roomId 直播间ID
   * @param accountType 账户类型，用于区分不同的直播间
   */
  public async analyzeUsersByRoomId(roomId: string, accountType: YuHeAccountType) {
    try {
      // 检查是否已经处理过该 roomId
      if (DanmuAnalyzer.processedRoomCache.has(roomId)) {
        logger.log(`[DanmuAnalyzer] roomId ${roomId} 已经处理过，跳过`)
        return
      }

      logger.log(`[DanmuAnalyzer] 开始处理直播间 ${roomId}，账户类型: ${accountType}`)

      // 1. 根据账户类型创建对应的 XingyanAPI 实例
      const xingyanAPI = this.createXingyanAPIByAccountType(accountType)

      // 2. 获取指定直播间内的所有客户
      const customers = await xingyanAPI.getAllWhiteListUsers(Number(roomId))
      if (!customers || customers.length === 0) {
        logger.warn(`[DanmuAnalyzer] roomId ${roomId} 没有白名单客户数据`)
        return
      }

      // 3. 创建并发限制
      const limit = pLimit(10)

      // 4. 处理每个客户的弹幕
      const promises: Promise<void>[] = []

      for (const customer of customers) {
        if (!customer.account) {
          logger.warn(`[DanmuAnalyzer] 跳过没有手机号的客户: ${JSON.stringify(customer)}`)
          continue
        }

        // 获取客户的chatId
        const chat = await chatDBClient.getChatByPhone(customer.account)
        if (!chat) {
          logger.warn(`[DanmuAnalyzer] 跳过没有chatId的客户: ${customer.account}`)
          continue
        }

        // 获取客户的当前课程天数
        const currentTime = await DataService.getCurrentTime(chat.id)
        if (!currentTime.is_course_day || currentTime.day < 1 || currentTime.day > 4) {
          logger.warn(`[DanmuAnalyzer] 客户 ${customer.account} 当前不在课程期间，day=${currentTime.day}, is_course_day=${currentTime.is_course_day}`)
          continue
        }

        promises.push(
          limit(() => this.processUser(roomId, customer.account, chat.id, currentTime.day, xingyanAPI, accountType))
        )
      }

      await Promise.all(promises)

      // 处理完成后，将 roomId 加入缓存
      DanmuAnalyzer.processedRoomCache.set(roomId, true)
      logger.log(`[DanmuAnalyzer] roomId ${roomId} 处理完成并加入缓存`)
    } catch (error) {
      logger.warn(`[DanmuAnalyzer] 处理直播间 ${roomId} 的弹幕失败:`, error)
    }
  }

  /**
   * 根据账户类型创建对应的 XingyanAPI 实例
   * @param accountType 账户类型
   * @returns XingyanAPI 实例
   */
  private createXingyanAPIByAccountType(accountType: YuHeAccountType): XingyanAPI {
    if (accountType === YuHeAccountType.ZhiCheng) {
      return new XingyanAPI({
        accountType: accountType,
        cropId: Config.setting.xingyan.zhicheng.cropId,
        secretKey: Config.setting.xingyan.zhicheng.secretKey
      })
    } else {
      return new XingyanAPI({
        accountType: accountType,
        cropId: Config.setting.xingyan.zhongshentong.cropId,
        secretKey: Config.setting.xingyan.zhongshentong.secretKey
      })
    }
  }
}