#!/bin/bash

# 如果任何命令执行失败，立即退出脚本
set -e

# ===== 配置变量 =====
# 远程 Docker 仓库地址 (!!! 如果需要，请更新 - 必须与 deploy.ts 一致 !!!)
REMOTE_REPO="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/yuhe" # 修改为 yuhe 的仓库路径
TAG_LATEST="latest"
DOCKER_COMPOSE_FILE="docker-compose.yaml" # 假设这是 docker 目录下的 compose 文件名
LOG_FILE="./deploy_script.log" # 服务器上 docker 目录中的日志文件

# --- 服务选择 ---
# 使用通过参数 ($@) 传递的服务。如果没有参数，则默认为 'yuhe1'。
if [ -n "$1" ]; then
    SERVICES_TO_DEPLOY=("$@")
else
    # 如果没有通过参数提供服务，则使用默认服务
    SERVICES_TO_DEPLOY=("yuhe1") # 修改默认服务为 yuhe1
fi

# --- 工具函数 ---
log() {
    # 将带时间戳的日志消息输出到标准输出和日志文件
    echo "$(date '+%Y-%m-%d %H:%M:%S') : $1" | tee -a "$LOG_FILE"
}

# 函数：检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 确定使用哪个 docker compose 命令
if command_exists docker-compose; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif command_exists docker compose; then
    # 注意空格 - 这是较新的插件语法
    DOCKER_COMPOSE_CMD="docker compose"
else
    log "错误：未找到 'docker-compose' 或 'docker compose' 命令。请安装 Docker Compose。" # 中文日志
    exit 1
fi
log "使用 Docker Compose 命令: $DOCKER_COMPOSE_CMD" # 中文日志


# ===== 主部署流程 =====

log "===== 服务器端部署脚本启动 =====" # 中文日志

# 本次运行清空日志文件 (可选)
# > "$LOG_FILE"

log "正在部署的服务: ${SERVICES_TO_DEPLOY[*]}" # 中文日志
log "使用 Docker Compose 文件: $DOCKER_COMPOSE_FILE" # 中文日志
log "从仓库拉取最新镜像: $REMOTE_REPO:$TAG_LATEST" # 中文日志

# 1. 为指定服务拉取最新镜像
log "拉取镜像 ..." # 中文日志
if $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" pull yuhe1; then
    log "镜像拉取成功。" # 中文日志
else
    log "错误：拉取镜像失败。请检查仓库/标签是否存在以及权限。" # 中文日志
    # 可选地退出，或继续处理其他服务？为安全起见退出。
    exit 1
fi

# 遍历每个作为参数传递的服务
for SERVICE in "${SERVICES_TO_DEPLOY[@]}"; do
    log "--- 开始处理服务: $SERVICE ---" # 中文日志

    # 3. 以分离模式启动服务容器
    #    --no-deps: 只启动指定服务，不启动其依赖项
    log "启动 $SERVICE 容器..." # 中文日志
    if docker rm -f $SERVICE && $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" up -d --no-deps "$SERVICE"; then
        log "$SERVICE 容器已启动。" # 中文日志
    else
        log "错误：启动 $SERVICE 容器失败。请检查 Docker 守护进程日志和容器日志。" # 中文日志
        exit 1
    fi

    # 4. 等待容器启动（5 秒）
    log "容器已启动，等待 5 秒后进行健康检查..."
    sleep 5

    # 5. 健康检查
    log "开始对 $SERVICE 进行健康检查..." # 中文日志
    CONTAINER_ID=$($DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" ps -q "$SERVICE")

    if [ -z "$CONTAINER_ID" ]; then
        log "错误：启动后无法找到 $SERVICE 的容器 ID。" # 中文日志
        log "$SERVICE 部署失败。" # 中文日志
        exit 1
    fi
    log "$SERVICE 容器 ID: $CONTAINER_ID" # 中文日志

    STATUS=$(docker inspect -f '{{.State.Status}}' "$CONTAINER_ID" 2>/dev/null)
    log "容器 $SERVICE 状态: $STATUS" # 中文日志

    if [ "$STATUS" != "running" ]; then
        log "错误：容器 $SERVICE 未处于 'running' 状态 (当前: $STATUS)。" # 中文日志
        log "请检查容器日志: $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" logs --tail=100 "$SERVICE"" # 中文日志
        log "$SERVICE 部署失败。" # 中文日志
        exit 1
    fi

    # --- 基于端口的健康检查 (使用 curl) ---
    # 提取映射到服务主容器端口的 HOST 端口
    # 尝试查找第一个4位以上的数字，假定它是主机端口
    # 如果端口格式不同或暴露了多个端口，可能需要调整
    EXPOSED_PORT=$(docker port "$SERVICE" | grep -o -m 1 '[0-9]\{4\}' | uniq)

    if [ -z "$EXPOSED_PORT" ]; then
        log "警告：无法自动检测 $SERVICE 的暴露端口。跳过 curl 检查。" # 中文日志
        # 决定这是失败还是仅仅是警告
        # log "$SERVICE 部署可能未完成（无法验证端口）。"
        # exit 1 # 取消注释可使其成为硬性失败
    else
        log "检查主机端口可访问性: $EXPOSED_PORT" # 中文日志
        retry_count=0
        max_retries=5
        retry_delay=4 # 秒

        while [[ $retry_count -lt $max_retries ]]; do
            # 使用 curl 的 -f 选项在 HTTP 错误 (>=400) 时静默失败
            # 连接超时设置为 5 秒
            if curl --fail --silent --show-error --max-time 5 "http://localhost:$EXPOSED_PORT" > /dev/null 2>&1; then
                log "健康检查通过：$SERVICE 在 http://localhost:$EXPOSED_PORT 可访问" # 中文日志
                break # 成功时退出重试循环
            else
                retry_count=$((retry_count + 1))
                if [[ $retry_count -lt $max_retries ]]; then
                    log "健康检查失败：$SERVICE 在端口 $EXPOSED_PORT 不可访问。将在 ${retry_delay} 秒后重试... ($retry_count/$max_retries)" # 中文日志
                    sleep $retry_delay
                else
                    log "错误：$SERVICE 在端口 $EXPOSED_PORT 的健康检查在 $max_retries 次重试后失败。" # 中文日志
                    log "请检查容器日志: $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" logs --tail=100 "$SERVICE"" # 中文日志
                    log "$SERVICE 部署失败。" # 中文日志
                    exit 1
                fi
            fi
        done # 结束重试循环
    fi # 结束端口检查

    log "$SERVICE 部署成功！" # 中文日志
    log "-----------------------------------------"
done # 结束服务循环

log "===== 服务器端部署流程成功结束 =====" # 中文日志
exit 0