import { <PERSON><PERSON><PERSON><PERSON> } from 'lib/date/date'
import dayjs from 'dayjs'
import { IW<PERSON>elistUser, XingyanAPI, YuHeAccountType } from 'model/xingyan'
import { IChattingFlag } from '../../state/user_flags'
import { JuziAPI } from 'model/juzi/api'
import { RegexHelper } from 'lib/regex/regex'
import logger from 'model/logger/logger'

import { getBotId, getUserId } from 'config/chat_id'
import { Config } from 'config'
import { catchError } from 'lib/error/catchError'
import { GroupNotification } from 'service/group_notification/group_notification'
import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { getVisualizedSopQueueName, VisualizedSopTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { ScheduleTask } from 'service/visualized_sop/schedule'
import { ExtractUserSlots } from '../../user_slots/user_slots_extraction'
import { SopScheduleTime } from 'service/visualized_sop/visualized_sop_type'
import { chatDBClient, chatStateStoreClient, eventTrackClient } from '../../service/instance'
import { PrismaMongoClient } from '../../database/prisma'

export interface IScheduleTime {
  is_course_day?: boolean // true 表示上课日，false 表示上课前
  post_course_day?: number // 课程结束后的第几天，1 表示第一天，2 表示第二天，依此类推
  day: number // 1-7 表示上课日第几天，负数代表上课前的几天，例如 0 表示上课前1天，-1 表示上课前2天，依此类推
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

export interface ICourseInfo {
  liveId: number
  liveName: string
  day: number
  liveLink: string
}

export async function calTaskTime(time: SopScheduleTime, chat_id: string): Promise<Date | undefined> {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatInfo = await mongoClient.chat.findUnique({ where:{
    id:chat_id
  }, select:{
    course_no:true
  } })

  if (!chatInfo?.course_no) {
    return undefined
  }
  const courseStartTime = DataService.getCourseStartTime(chatInfo.course_no)
  const day0 = dayjs(courseStartTime).hour(0).minute(0).second(0).subtract(1, 'day')
  const timeStringSplited = time.time.split(':')
  return day0.add(time.week * 7 + time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
}

export function calGroupTaskTime(time:SopScheduleTime, courseNo:number): Date {
  const courseStartTime = DataService.getCourseStartTime(courseNo)
  const day0 = dayjs(courseStartTime).hour(0).minute(0).second(0).subtract(1, 'day')
  const timeStringSplited = time.time.split(':')
  return day0.add(time.week * 7 + time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
}

export class DataService {
  static async getChatsByCourseNo(courseNo: number) {
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: courseNo
      }
    })

    return chats.filter((chat) => !Config.isInternalMember(chat.contact.wx_id) && !chat.id.includes('local'))
  }


  public static async getCurrentTime(chatId: string): Promise<IScheduleTime> {
    const startDate = await this.getCourseStartTimeByChatId(chatId)

    const currentDate = dayjs().toDate()

    // 计算当前时间与开课时间的天数差
    const timeDiffInDays = dayjs(currentDate)
      .startOf('day')
      .diff(dayjs(startDate).startOf('day'), 'day')

    const dayDiff = timeDiffInDays + 1

    const scheduleTime: IScheduleTime = {
      day: dayDiff, //后续会更新
      time: DateHelper.formatDate(currentDate, 'HH:mm:ss'),
    }

    if (dayDiff < 1) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
    } else if (dayDiff >= 1 && dayDiff <= 4) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = true
    } else {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
      scheduleTime.post_course_day = dayDiff - 4
    }

    return scheduleTime
  }

  public static async getCurrentTimeByDate(chatId: string, date: Date): Promise<IScheduleTime> {
    const startDate = await this.getCourseStartTimeByChatId(chatId)

    const currentDate = dayjs(date).toDate()

    // 计算当前时间与开课时间的天数差
    const timeDiffInDays = dayjs(currentDate)
      .startOf('day')
      .diff(dayjs(startDate).startOf('day'), 'day')

    const dayDiff = timeDiffInDays + 1

    const scheduleTime: IScheduleTime = {
      day: dayDiff, //后续会更新
      time: DateHelper.formatDate(currentDate, 'HH:mm:ss'),
    }

    if (dayDiff < 1) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
    } else if (dayDiff >= 1 && dayDiff <= 4) {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = true
    } else {
      scheduleTime.day = dayDiff
      scheduleTime.is_course_day = false
      scheduleTime.post_course_day = dayDiff - 4
    }

    return scheduleTime
  }

  public static async getCourseStartTimeByChatId(chatId: string) {
    let courseNo = await chatDBClient.getCourseNo(chatId)
    if (!courseNo) {
      //找不到则认为刚加入
      courseNo = this.getCurrentCourseNo()
    }
    return this.getCourseStartTime(courseNo)
  }

  public static async bindPhoneFromRemark(chatId: string, botId?: string) {
    const mongoClient = PrismaMongoClient.getInstance()
    const info = await mongoClient.chat.findFirst({
      where: { id: chatId },
      select: { phone: true },
    })
    let phoneNumber = info?.phone ?? ''
    if (phoneNumber && phoneNumber != '') {
      return phoneNumber
    }

    const userId = getUserId(chatId)

    phoneNumber = await this.findPhoneNumberByRemark(userId, botId)

    if (phoneNumber && phoneNumber != '') {
      logger.trace({ chat_id: chatId }, '备注已绑定手机号')

      await this.bindPhone(chatId, phoneNumber)
      return phoneNumber
    }
    return null
  }

  public static getCurrentCourseNo(): number {
    // 直接按格式输出并转成数字
    const now = dayjs()
    if (now.hour() < 12) {
      return Number(now.subtract(1, 'day').format('YYYYMMDD'))
    } else {
      return Number(now.format('YYYYMMDD'))
    }
  }

  public static async getCourseNoByChatId(chatId: string): Promise<number | null> {
    const mongoClient = PrismaMongoClient.getInstance()
    const chatInfo = await mongoClient.chat.findFirst({
      where: { id: chatId },
    })
    return chatInfo?.course_no ?? null
  }

  public static getCourseStartTime(courseNo: number): Date {
    return dayjs(courseNo.toString(10))
      .add(1, 'day')
      .hour(18)
      .minute(50)
      .second(0)
      .toDate()
  }

  public static async getCourseInfoByChatId(
    chatId: string,
  ): Promise<ICourseInfo[] | null> {
    const mongoClient = PrismaMongoClient.getInstance()
    const chatInfo = await mongoClient.chat.findFirst({
      where: { id: chatId },
    })
    if (!chatInfo?.course_no) return null
    return await this.getCourseInfoByCourseNo(chatInfo.course_no)
  }

  public static async getCourseInfoByCourseNo(
    courseNo: number,
  ): Promise<ICourseInfo[] | null> {
    const courseDate = dayjs(courseNo.toString(10))
      .add(1, 'day')
      .format('YY/M/D')

    const data = await XingyanAPI.getRoomGroupInfo(`${courseDate}中神通`)

    if (!data) {
      return null
    }

    function extractFirstNumber(str: string) {
      const match = str.match(/\d+/)
      return match ? Number(match[0]) : 0
    }

    return data.detailList.map((item) => ({
      liveId: item.roomId,
      liveName: item.roomName,
      day: extractFirstNumber(item.roomName),
      liveLink: item.shortUrl, // 注意直播链接和回放链接是同一个
    }))
  }

  // day 应该在[1,4]的范围中
  public static async getCourseLink(
    chatId: string,
    day: number,
  ): Promise<string | null> {
    if (day > 4 || day < 1) return null
    const courseNo = await this.getCourseNoByChatId(chatId)
    if (!courseNo) return null
    return this.getCourseLinkByCourseNo(courseNo, day)
  }

  public static async getCourseLinkByCourseNo(courseNo:number, day:number):Promise<string|null> {
    const courseInfo = await this.getCourseInfoByCourseNo(courseNo)
    if (!courseInfo) return null
    const courseInfoMap = this.getCourseDayMap(courseInfo)
    const thatDayCourseInfo = courseInfoMap.get(day)
    if (thatDayCourseInfo) {
      return thatDayCourseInfo.liveLink
    } else {
      return null
    }
  }

  /**
   * 主动获取课程完成情况，课程可以传 1，2，3，4
   */
  public static async isCompletedCourse(
    chatId: string,
    day: number,
  ): Promise<boolean> {
    const min = 45
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    if (day >= 1 && day <= 4) {
      if (state[`is_complete_course_day${day}`]) {
        return true
      } else {
        const result = await this.isAttendCourseMoreThanCertainDuration(chatId, day, min)
        if (result) {
          await chatStateStoreClient.update(chatId, { state:<IChattingFlag>{ ...state, [`is_attend_course_day${day}`]:true, [`is_complete_course_day${day}`]:true } })
          eventTrackClient.track(chatId, IEventType.CourseComplete, { 'day': day })
          return true
        } else {
          return false
        }
      }
    } else {
      throw (`param day is wrong, day is ${day}`)
    }
  }

  public static async getUserName(chatId:string) {
    const name = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chatId, '基本信息', '称呼')
    if (name && name.length > 0 && name.length < 4 && (name[name.length - 1] == '哥' || name[name.length - 1] == '姐') && name != '小姐' && name != '小哥') {
      return name
    }
    return '老板'
  }

  public static async isDoingDouyin(chatId:string) {
    await new ExtractUserSlots().extractUserSlots(chatId, 6, { chat_id:chatId })
    const state = await chatStateStoreClient.get(chatId)
    const userSlotsRecord = state.userSlots
    if (!userSlotsRecord) return false
    const userSlots = UserSlots.fromRecord(userSlotsRecord)

    if (userSlots.isTopicSubTopicExist('抖音运营状态', '是否抖音在做') && userSlots.getSubTopicContent('抖音运营状态', '是否抖音在做') != '否') return true
    return false
  }

  public static async isAttendCourse(
    chatId: string,
    day: number,
  ): Promise<boolean> {
    try {
      const min = 1
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      if (day >= 1 && day <= 4) {
        if (state[`is_attend_course_day${day}`]) {
          return true
        } else {
          const result = await this.isAttendCourseMoreThanCertainDuration(chatId, day, min)
          if (result) {
            await chatStateStoreClient.update(chatId, { state:<IChattingFlag>{ ...state, [`is_attend_course_day${day}`]:true } })
            eventTrackClient.track(chatId, IEventType.CourseArrive, { 'day': day })
            return true
          } else {
            return false
          }
        }
      } else {
        logger.error(`param day is wrong, day is ${day}`)
        return false
      }
    } catch (e) {
      return false
    }
  }

  //判断是否上课超过一定分钟数
  public static async isAttendCourseMoreThanCertainDuration(
    chatId: string,
    day: number,
    min: number,
  ): Promise<boolean> {
    try {
      const chat = await chatDBClient.getById(chatId)
      if (!chat) {
        throw new Error('chat is not found')
      }

      if (!chat.course_no) {
        throw new Error('courseNo is not set')
      }

      const courseInfo = await this.getCourseInfoByCourseNo(chat.course_no)
      if (!courseInfo) {
        throw new Error(`找不到课程信息: ${chat.course_no}`)
      }

      const courseMap = this.getCourseDayMap(courseInfo)

      // 查询对应的完课时间
      const [liveWatchTime, vodWatchTime] = await Promise.all([
        XingyanAPI.getLiveStreamWatchTime(
          courseMap.get(day)?.liveId.toString() ?? '',
          chat.phone,
        ),
        XingyanAPI.getRoomRecordingWatchingTime(
          courseMap.get(day)?.liveId.toString(10) ?? '',
          chat.phone,
        ),
      ])

      return Boolean(
        (liveWatchTime && liveWatchTime >= min * 60) ||
          (vodWatchTime && vodWatchTime >= min * 60),
      )
    } catch (e) {
      logger.error('判断是否上课超过一定分钟数失败', e)

      return false
    }
  }

  static async isPaidSystemCourse(chatId: string): Promise<boolean> {
    if (
      (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_complete_payment
    ) {
      return true
    }

    const chat = await chatDBClient.getById(chatId)
    if (!chat) {
      return false
    }
    if (!chat.phone) {
      return false
    }

    const [error, orders] = await catchError(XingyanAPI.getOrderInfoPage(chat.phone))
    if (error) {
      return false
    }

    return Boolean(orders && orders.list && orders.list.length > 0)
  }

  public static async findPhoneNumberByRemark(userId: string, botId?: string) {
    // 读取备注中是否包含电话号码
    const phoneNumber = await JuziAPI.getCustomerPhoneNumber(
      botId ? botId : (Config.setting.wechatConfig?.id as string),
      userId,
    )
    if (
      phoneNumber &&
      phoneNumber.length >= 1 &&
      phoneNumber.some((phone) => RegexHelper.extractPhoneNumber(phone))
    ) {
      return phoneNumber.find((phone) =>
        RegexHelper.extractPhoneNumber(phone),
      ) as string
    }

    return ''
  }

  private static getCourseDayMap(courseInfo: ICourseInfo[]) {
    const courseMap = new Map<number, ICourseInfo>()

    for (const info of courseInfo) {
      courseMap.set(info.day, info)
    }

    return courseMap
  }

  public static async getSystemCourseStartTime(): Promise<string> {
    const data = await XingyanAPI.getRoomGroupInfo('系统陪跑班最新期')

    if (!data) {
      throw new Error('系统陪跑班最新期分组未配置')
    }

    // 过滤掉 startTime 为空字符串的课程
    const validCourses = data.detailList.filter(
      (course) => course.startTime && course.startTime.trim() !== '',
    )

    if (validCourses.length === 0) {
      throw new Error('没有找到有效的课程开始时间')
    }

    // 找出最早的开课时间
    const earliestCourse = validCourses.reduce((earliest, current) => {
      const earliestTime = new Date(earliest.startTime).getTime()
      const currentTime = new Date(current.startTime).getTime()
      return currentTime < earliestTime ? current : earliest
    }, validCourses[0])

    // 返回最早的开课时间字符串
    return earliestCourse.startTime
  }

  static async importWhiteListOfYesterdayUsers() {
    // 查询昨天全天进入有手机号的客户，提前导入到白名单中
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: Number(dayjs().subtract(1, 'day').format('YYYYMMDD'))
      }
    })

    // 导入两次
    const clients = [
      new XingyanAPI(),
      new XingyanAPI(
        {
          accountType: YuHeAccountType.ZhiCheng,
          cropId: Config.setting.xingyan.zhicheng.cropId,
          secretKey: Config.setting.xingyan.zhicheng.secretKey
        }
      )
    ]

    for (const client of clients) {
      // 获取今天的直播间信息
      const liveRooms = await DataService.getLiveStreamCourseInfoByDate(new Date(), client)

      // 这里假设已经校验过直播间配置了
      if (!liveRooms) {
        return
      }

      // 构建待导入客户
      const importUsers: IWhitelistUser[] = []

      for (const chat of chats) {
        if (chat.id.includes('null')) {
          continue
        }

        if (chat.id.includes('local')) {
          continue
        }

        if (chat.id.includes(' ')) {
          continue
        }

        if (chat.phone) {
          importUsers.push({
            account: chat.phone,
            name: chat.contact.wx_name,
          })
        } else {
          if (Config.isInternalMember(chat.contact.wx_id)) { // 内部客户不通知
            continue
          }

          // 校验下是否有备注
          const isBindSuccess = await DataService.bindPhoneFromRemark(
            chat.id,
            chat.wx_id,
          )

          if (!isBindSuccess) {
            await GroupNotification.notify(`${chat.contact.wx_name} 请手动绑定手机号, 否则今晚和后续客户会无法进入直播间`, 'R:*****************', '****************')
          }
        }
      }

      // 待导入中有不正常手机号进行报警
      const unusualPhones = importUsers.filter((importUser) => importUser.account.length != 11)
      for (const unusualPhone of unusualPhones) {
        await GroupNotification.notify(`异常手机号 ${JSON.stringify(unusualPhone)}, ${unusualPhone.account.length} 位`,  'R:*****************', '****************')
      }

      // 对四个直播间导入客户
      await Promise.allSettled(
        liveRooms.detailList.map(async (liveRoom) => {
          let usersToImport = importUsers

          // 待导入的需要按照已有白名单进行去重
          const currentWhiteList = await client.getAllWhiteListUsers(
            liveRoom.roomId
          )

          if (currentWhiteList && currentWhiteList.length > 0) {
            const existingAccounts = new Set(currentWhiteList.map((u) => u.account))
            usersToImport = usersToImport.filter((user) => !existingAccounts.has(user.account))
          }

          usersToImport = ObjectUtil.removeDuplicateByKey(usersToImport, ['account'])

          const [err, data] = await catchError(
            client.importUsersInBatches(liveRoom.roomId, usersToImport)
          )

          if (err || data === false) {
            await GroupNotification.notify(`导入直播间 ${DataService.getCurrentCourseNo().toString()} 白名单失败 ${err?.message ?? ''}`,  'R:*****************', '****************')
          }
        })
      )

      logger.log('导入白名单成功')
    }
  }

  /*
   * 获取今晚为第一课的直播信息，注意进入今晚直播的客户是昨天进入的，不是今天当期
   */
  public static async getLiveStreamCourseInfoByDate(date: Date, client?: XingyanAPI) {
    const courseDate = dayjs(date).format('YY/M/D')

    if (client) {
      return await client.getRoomGroupInfo(`${courseDate}中神通`)
    }

    // 获取今天的直播间信息
    return await XingyanAPI.getRoomGroupInfo(`${courseDate}中神通`)
  }

  public static async getIpByChatId(chatId: string): Promise<string> {
    const mongoClient = PrismaMongoClient.getInstance()
    const chat = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (!chat) { return '' }
    const courseNo = await this.getCourseNoByChatId(chatId) ?? 2025
    const ipTable = await mongoClient.ip_table.findMany({ where:{ OR:[{ start_course_no:{ lte: courseNo } }, { end_course_no:{ gte: courseNo } }] } })
    return ipTable.find((item) => item.account == chat.wx_id && item.start_course_no <= (chat.course_no_ori ?? chat.course_no ?? 0) && (chat.course_no_ori ?? chat.course_no ?? 0) <= item.end_course_no)?.ip ?? ''
  }

  public static async bindPhone(chatId: string, phone: string) {
    const mongoClient = PrismaMongoClient.getInstance()
    const chatInfo = await mongoClient.chat.findFirst({
      where: { id: chatId },
    })
    if (!chatInfo) {
      logger.error(`找不到这个客户:${chatId}`)
      return
    }

    await mongoClient.chat.update({ where: { id: chatId }, data: { phone } })

    if (!chatInfo?.course_no) {
      logger.warn(`${chatInfo.id} 找不到课程信息`)
      return
    }
    const botId = chatInfo.wx_id
    const xingYanClient:XingyanAPI = XingyanAPI.getXingYanClientByBotId(botId)

    await this.addBoardcastWhiteList(
      xingYanClient,
      chatInfo.course_no,
      chatInfo.contact.wx_name,
      phone
    )
  }

  static async addBoardcastWhiteList(
    xingYanClient:XingyanAPI,
    courseNo: number,
    name: string,
    phone: string,
  ) {
    const courseInfo = await this.getCourseInfoByCourseNo(courseNo)
    if (!courseInfo) {
      logger.error('找不到课程信息')
      return
    }

    await Promise.allSettled(
      courseInfo.map(async (liveRoom) => {
        const [err, _] = await catchError(
          xingYanClient.addImportUser(liveRoom.liveId, [
            {
              account: phone,
              name: name,
            },
          ]),
        )
        if (err) {
          logger.error(`导入直播间 ${liveRoom.liveId} 白名单失败`, err)
        }
      }),
    )
  }

  /**
   * 判断当前时间是否在指定课程时间范围内
   * @param chatId 聊天ID，用于获取当前时间
   * @param timeline 时间线类型，可选值：'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse'，默认为 'inCourse'
   * @param day 天数，可选参数，默认为当前天
   * @returns 返回布尔值，表示是否在指定时间范围内
   */
  public static async isInCourseTimeLine(chatId: string, timeline: 'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse' = 'inCourse', day?: number) {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (!day) {
      day = currentTime.day
    }

    // 课程时间配置
    const COURSE_TIMES: Record<number, Record<string, string>> = {
      1: { before: '18:50:00', after: '21:42:00', sales: '21:42:00' },
      2: { before: '18:50:00', after: '22:41:00', sales: '20:43:00' },
      3: { before: '18:50:00', after: '22:17:00', sales: '20:20:00' },
      4: { before: '18:50:00', after: '22:00:00', sales: '20:05:00' },
    }

    // 处理跨天情况的核心逻辑

    // 非当天或课程时间内inCourse返回false
    if (currentTime.day != day && timeline === 'inCourse' && day < 1 && day > 5) {
      return false
    }
    // 天数更小beforeCourse为true
    if (currentTime.day < day) {
      return timeline === 'beforeCourse'
    }
    // 天数更小afterSales为false
    if (currentTime.day < day && timeline === 'afterSales') {
      return false
    }
    // 天数更大afterCourse和afterSales为true
    if (currentTime.day > day) {
      return timeline === 'afterCourse' || timeline === 'afterSales'
    }
    // 开课前beforeCourse为true
    if (currentTime.day <= 0) {
      return timeline === 'beforeCourse'
    }
    // 开课前afterSales为false
    if (timeline === 'afterSales' && day < 1) {
      return false
    }
    // 课程结束afterCourse和afterSales为true
    if (currentTime.day >= 5) {
      return timeline === 'afterCourse' || timeline === 'afterSales'
    }

    // 处理当天情况的核心逻辑
    const { before: beforeTime, after: afterTime, sales: salesTime } = COURSE_TIMES[day]
    if (timeline === 'inCourse') {
      return DateHelper.isTimeBefore(currentTime.time, afterTime) && DateHelper.isTimeAfter(currentTime.time, beforeTime)
    }
    if (timeline === 'beforeCourse') {
      return DateHelper.isTimeBefore(currentTime.time, beforeTime)
    } else if (timeline === 'afterCourse') {
      return !DateHelper.isTimeBefore(currentTime.time, afterTime)
    } else if (timeline === 'afterSales') {
      return salesTime ? !DateHelper.isTimeBefore(currentTime.time, salesTime) : false
    }

    return false
  }

  public static async getTodayCourse(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    let todayCourse = ''
    let tomorrowCourse = ''
    if (currentTime.day > 0 && currentTime.day < 5) {
      const isAfterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', currentTime.day)
      const isInCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse', currentTime.day)
      const courseStatus = isAfterCourse ? '（已结束）' : (isInCourse ? '（已开始）' : '')
      todayCourse  = `今日课程：第${currentTime.day}课${courseStatus}。`
    } else if (currentTime.day >= 5) {
      todayCourse = '速成班课程已结束'
    }
    if (currentTime.day >= 0 && currentTime.day <= 3) {
      tomorrowCourse = `明日课程：第${currentTime.day + 1}课`
    }
    return `${todayCourse}${tomorrowCourse}`
  }

  public static async delayCourseNo(chatId:string, courseNo:number) {
    Config.setting.projectName = 'yuhe'
    const userId = getUserId(chatId)
    const botId = getBotId(chatId)
    await VisualizedSopTasks.clearSop('yuhe', chatId, botId)
    const mongoClient = PrismaMongoClient.getInstance()
    const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (!chatInfo) {
      logger.log(`找不到chat ${chatId}`)
      return
    }
    if (courseNo == chatInfo?.course_no) {
      logger.log(`延期失败,chat_id:${chatId}`)
      return
    }
    // update chat info
    await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no_ori:chatInfo?.course_no_ori ?? chatInfo?.course_no, course_no:courseNo } })

    // insert sop
    const tasks = await VisualizedSopTasks.getTaskList('yuhe', userId, chatId, botId)
    await Promise.all(
      tasks.map(async (task) => {
        task.sendTime = await calTaskTime(
          task.scheduleTime,
          task.chatId
        )
      })
    )
    const filtedTasks = tasks.filter((task) => {
      const day = task.scheduleTime.week * 7 + task.scheduleTime.day
      if (day < 1 || (day == 1 && task.scheduleTime.time < '08:00:00')) {
        return false
      } else {
        return true
      }
    })
    const filtedTasksWithForce = filtedTasks.map((task) => ({ ...task, force:true }))
    await ScheduleTask.addTasks(getVisualizedSopQueueName('yuhe', botId), filtedTasksWithForce)

    // update whitelist of boardcast

    const xingYanClient:XingyanAPI = XingyanAPI.getXingYanClientByBotId(botId)

    if (chatInfo?.course_no && chatInfo?.phone) {
      const courseInfo = await this.getCourseInfoByCourseNo(chatInfo.course_no)
      if (!courseInfo) {
        logger.error('找不到课程信息')
        return
      }

      await Promise.allSettled(
        courseInfo.map(async (liveRoom) => {
          if (chatInfo.phone) {
            const [err, _] = await catchError(
              xingYanClient.deleteImportUser(liveRoom.liveId, [chatInfo.phone]),
            )
            if (err) {
              logger.error(`删除直播间 ${liveRoom.liveId} 白名单失败`, err)
            }
          }
        }),
      )
    }
    if (chatInfo.phone) {
      await this.addBoardcastWhiteList(
        xingYanClient,
        courseNo,
        chatInfo.contact.wx_name,
        chatInfo.phone,
      )
    }
  }
}