import { randomSleep } from 'lib/schedule/schedule'
import { JuziAPI } from 'model/juzi/api'
import { Config, YuHeAccountType } from 'config'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { getBotId, getUserId } from 'config/chat_id'
import logger from 'model/logger/logger'
import { Retry } from 'lib/retry/retry'
import { RedisDB } from 'model/redis/redis'
import { PrismaMongoClient } from '../../database/prisma'


export class AddUserToGroup {
  static groupMaxNumber = 160

  private static async addUserToGroupFunction(chatId: string, waitTime: number = 3 * 60 * 1000) {
    // 建议拉人进群时，每分钟不超过50次： 所以随机到 3 分钟内
    await randomSleep(100, waitTime)// 等待随机秒数

    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (!userInfo) {
      throw new Error(`没有找到这个客户 chatId: ${chatId}`)
    }
    if (!userInfo.course_no) {
      throw new Error(`没有找到这个客户的course_no chatId: ${chatId}`)
    }
    let groupInfo = await PrismaMongoClient.getInstance().group.findMany({
      where: {
        course_no:userInfo.course_no
      },
      orderBy:{ group_id:'asc' }
    })

    if (!groupInfo || groupInfo.length == 0) {
      throw new Error('获取账号群信息失败')
    }
    const botId = getBotId(chatId)
    const botType = Config.getYuHeAccountType(botId)
    if (botType == YuHeAccountType.ZhiCheng) {
      groupInfo = groupInfo.filter((item) => ['AI获客4天速成班1', 'AI获客4天速成班2', 'AI获客4天速成班3', 'AI获客4天速成班4', 'AI获客4天速成班11', 'AI获客4天速成班22', 'AI获客4天速成班33', 'AI获客4天速成班44'].includes(item.name))
    } else {
      groupInfo = groupInfo.filter((item) => !(['AI获客4天速成班1', 'AI获客4天速成班2', 'AI获客4天速成班3', 'AI获客4天速成班4', 'AI获客4天速成班11', 'AI获客4天速成班22', 'AI获客4天速成班33', 'AI获客4天速成班44'].includes(item.name)))
    }
    let inviteGroupId = groupInfo[0].group_id
    const redisClient = RedisDB.getInstance()
    for (const group of groupInfo) {
      const number = Number(redisClient.get(AddUserToGroup.getGroupNumberRedisKey(group.group_id)) ?? 0)
      let groupMaxNumber = 160
      if (!(['AI获客4天速成班1', 'AI获客4天速成班2', 'AI获客4天速成班3', 'AI获客4天速成班4'].includes(group.name))) {
        groupMaxNumber = 460
      }
      if (number < AddUserToGroup.groupMaxNumber) {
        inviteGroupId = group.group_id
        break
      }
    }

    const response = await JuziAPI.addToGroup({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      contactWxid: getUserId(chatId),
      roomWxid: inviteGroupId
    })

    if (response.errcode !== undefined && response.errcode !== 0) {
      throw new Error('添加客户到群失败')
    }
    await redisClient.incr(AddUserToGroup.getGroupNumberRedisKey(inviteGroupId))
  }

  public static getGroupNumberRedisKey(groupId:string) {
    return `group_number_${groupId.replaceAll(':', '')}`
  }


  public static async addUserToGroup(chatId: string, waitTime: number = 3 * 60 * 1000) {
    try {
      Retry.retry(4, async () => {
        await AddUserToGroup.addUserToGroupFunction(chatId, waitTime)
      }, {
        delayFunc :(retryCount) => {
          if (retryCount === 1) return 1 * 60 * 1000
          if (retryCount === 2) return 2 * 60 * 1000
          if (retryCount === 3) return 5 * 60 * 1000
          if (retryCount === 4) return 10 * 60 * 1000
          return 0  // 之后不再进行重试
        }
      })
    } catch (e) {
      logger.error(`拉群失败${e} chat_id:${chatId}`)
      await HumanTransfer.transfer(chatId, getUserId(chatId), HumanTransferType.FailedToJoinGroup, 'onlyNotify')
    }
  }
}





