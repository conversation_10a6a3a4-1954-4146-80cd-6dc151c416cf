import { UUID } from 'lib/uuid/uuid'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { RAG } from '../rag'
import { YuheSalesCase } from '../sales_case'


describe('yuhe_rag_test', () => {
  it('ragSearchTest', async () => {
    const question = '那他这个都是在整个大群里去去问吗？还是这种老师在那儿盯着个人那种'
    const round_id = UUID.v4()
    console.log('round_id', round_id)
    console.log(await RAG.search(question, 'test', round_id))
  }, 9E8)

  it('12312323', async () => {
    // const question = '陪跑营几号开始'
    // const round_id = UUID.v4()
    // console.log('round_id', round_id)
    // console.log(await YuheRag.search(question, 'test', round_id))

    const questions = [
      '谁来上课',
      '课程只有三天么',
      '怎么开蓝V',
      '你们公司在哪',
      '怎么拍不了',
      '钩子怎么设',
      '那些有电话的设为互相关注的人能看可以不',
      '你们线下去哪学',
      '就是你们先期这学习复杂不复杂呀，因为我也这个电脑，手机呀，什么剪辑也都不是说特别会弄。',
      '如果不用了可以退么',
      '人设怎么改',
      '怎么看回放',
      '有回放么',
      '粉丝少怎么办',
      '几号开始',
      '今天没空学习过后还可以吗',
      '我现在就想知道，如果跟你们学的话，但是你能保证这个账号几天能起来，流量几天能起来。',
      '还有一个就是我们老号可不可以做．还是我们重新起那个新号。',
      '报了这个2980的课程的话，玉姐她还赠送线下的课程？是吧',
      '线下课什么时候开课',
      '2980什么时候开课',
      '那个线下课不是每个星期每个月都有的吗？到时是根据自己的时间来提前安排是吧',
      '陪跑营除了给予这些方案还会一步一步指导怎么写文案和录视频嘛',
      '陪跑营每天需要发多少时间来学习和做这个事情呢？',
      '每天上课多久',
      '几点上课',
    ]

    for (const question of questions) {
      const round_id = UUID.v4()
      console.log(question, '\n search result:', await RAG.search(question, 'test', round_id))
    }
  }, 9E8)
})