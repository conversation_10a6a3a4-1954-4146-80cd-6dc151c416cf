import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { PostAction } from './post_action'
import { DataService } from '../../helper/getter/get_data'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterAdding extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const afterAdding = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1)
    return Promise.resolve(afterAdding)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterAdding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterAdding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }
}