import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { DataService } from '../../helper/getter/get_data'

export class DuringCourse extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const duringCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
    return Promise.resolve(duringCourse)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.duringCourse)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.duringCourse)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve()
  }

  getGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }
}