import { GroupNotification } from 'service/group_notification/group_notification'
import { PrismaMongoClient } from '../database/prisma'
import { chatDBClient } from '../service/instance'

describe('Test', function () {
  beforeAll(() => {

  })


  it('开启 AI', async () => {
    // 获取 15:30 候创建的客户
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        created_at: {
          lte: new Date('2025-06-17 15:30:00'),
        },
      },
    })

    for (const chat of chats) {
      if (chat.id.startsWith('16888')) {
        await chatDBClient.setHumanInvolvement(chat.id, false)
        // 关 SOP
        await chatDBClient.setStopGroupPush(chat.id, false)
      }
    }

  }, 30000)

  it('关闭 AI', async () => {
    // 获取 15:30 候创建的客户
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        created_at: {
          lte: new Date('2025-06-17 15:50:00'),
          gte: new Date('2025-06-17 15:20:00'),
        },
      },
    })

    for (const chat of chats) {
      if (chat.id.startsWith('16888')) {
        await chatDBClient.setHumanInvolvement(chat.id, true)
        // 关 SOP
        await chatDBClient.setStopGroupPush(chat.id, true)
      }
    }

  }, 30000)

  it('获取群 id', async () => {
    // Config.setting.wechatConfig = await loadConfigByWxId('1688858335726355')
    await GroupNotification.notify('Test', 'R:10815051791863856', '1688854546332791')

    // await GroupNotification.notify('AI 提醒测试', 'R:10933256292171603')


    // const ids = ['1688858335726355']
    //
    // for (const id of ids) {
    //   const rooms =  await JuziAPI.listGroup(id)
    //
    //   for (const room of rooms) {
    //     console.log(room.name, room.imRoomId, room.owner === id, room.owner)
    //   }
    // }
  }, 60000)
})