import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { wecomCommonMessageSender, wecomMessageSender } from '../service/instance'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'

describe('LatencyAnalyzer sendPersonalizedMessage 测试', () => {
  beforeAll(async () => {
    // 设置测试环境
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe_test')
    Config.setting.BOT_NAME = '大麦老师'
    Config.setting.projectName = 'yuhe'
  })

  it('spsp', async () => {
    await wecomCommonMessageSender.sendText('****************_1688854546332791', {
      type: SendMessageType.text,
      text: '测试',
      description: '测试'
    })
  }, 60000)

  it('pspsps', async () => {
    await wecomMessageSender.sendById({
      user_id: '****************',
      chat_id: '****************_1688854546332791',
      ai_msg: '测试'
    })
  }, 60000)
})
