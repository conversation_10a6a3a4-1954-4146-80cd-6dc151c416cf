import dayjs from 'dayjs'
import pLimit from 'p-limit'
import { DataService } from '../helper/getter/get_data'
import { chatHistoryServiceClient, wecomCommonMessageSender } from '../service/instance'
import { IDBBaseMessage } from 'service/chat_history/chat_history'
import logger from 'model/logger/logger'
import { LLM } from 'lib/ai/llm/llm_model'
import { ContextBuilder } from '../workflow/context'
import { getState } from 'service/llm/state'
import { getUserId } from 'config/chat_id'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { XMLHelper } from 'lib/xml/xml'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'

/** 客户延迟分析结果 */
interface CustomerLatencyData {
  chatId: string;
  replyDelays: number[]; // 每轮 assistant→user 的回复延迟（分钟）
  silenceDuration: number; // 最后一条 AI 消息到现在的沉默（分钟）
  lastAssistantMessage?: IDBBaseMessage;
}

/** 延迟趋势信息 */
interface DelayTrend {
  trend:
      | '回复延迟逐渐提高'
      | '回复延迟逐渐降低'
      | '回复延迟不变'
      | '对话轮数不足以支持延迟分析';
  slope?: number; // 斜率：每轮延迟增加/减少的分钟数
  r2?: number;
  averageDelay?: number; // 全局平均延迟
}

/** 个性化触达策略 */
interface PersonalizedStrategy {
  message: string; // 个性化消息内容
  delayMinutes: number; // 延迟发送时间（分钟）
  reasoning: string; // LLM 的推理过程
}

export class LatencyAnalyzer {
  /* ---------- 常量配置 ---------- */
  private static readonly REPLY_DELAY_THRESHOLD = 60 // min
  private static readonly SILENCE_THRESHOLD = 120 // min
  private static readonly MIN_DELAY_SAMPLES = 4 // 至少 4 轮才分析趋势
  private static readonly CONCURRENCY_LIMIT = 10 // 并发上限
  private static readonly TREND_SLOPE_THRESHOLD = 1 // min / turn

  /* ---------- 入口 ---------- */
  public static async analyzeAndScheduleMsg(): Promise<void> {
    const now = dayjs()
    const currentTime = now.toDate()

    // 近 5 天课程号
    const courseNos = Array.from({ length: 5 }, (_, i) =>
      parseInt(now.subtract(i, 'day').format('YYYYMMDD'), 10),
    )
    logger.log(`[LatencyAnalyzer] 开始分析课程期数: ${courseNos.join(', ')}`)

    /* ------- 1. 并发获取所有课程的客户 ------- */
    const limit = pLimit(this.CONCURRENCY_LIMIT)
    const chatLists = await Promise.all(
      courseNos.map((courseNo) =>
        limit(() =>
          DataService.getChatsByCourseNo(courseNo).catch((err) => {
            logger.error(`[LatencyAnalyzer] 获取课程 ${courseNo} 客户失败`, err)
            return []
          }),
        ),
      ),
    )
    const chats = chatLists.flat()
    logger.log(`[LatencyAnalyzer] 共获取 ${chats.length} 个客户`)

    /* ------- 2. 并发分析每个客户的延迟 ------- */
    const analysisResults = await Promise.allSettled(
      chats.map((chat) => limit(() => this.analyzeCustomerLatency(chat.id, currentTime))),
    )

    const customers: CustomerLatencyData[] = analysisResults
      .filter((r): r is PromiseFulfilledResult<CustomerLatencyData | null> => r.status === 'fulfilled')
      .map((r) => r.value)
      .filter((c): c is CustomerLatencyData => Boolean(c))
    logger.log(`[LatencyAnalyzer] 共分析 ${customers.length} 个客户`)

    /* ------- 3. 并发评估并调度消息 ------- */
    await Promise.all(
      customers.map((c) => limit(() => this.evaluateAndScheduleMessage(c, currentTime))),
    )

    logger.log('[LatencyAnalyzer] 延迟分析和消息调度完成')
  }

  /* ---------- 单客户分析 ---------- */
  private static async analyzeCustomerLatency(
    chatId: string,
    currentTime: Date,
  ): Promise<CustomerLatencyData | null> {
    try {
      const history = await chatHistoryServiceClient.getChatHistoryByChatId(chatId, true)
      if (history.length === 0) return null

      const replyDelays: number[] = []
      let lastAssistant: IDBBaseMessage | undefined

      for (let i = 0; i < history.length - 1; i++) {
        const cur = history[i]
        const next = history[i + 1]
        if (cur.role === 'assistant') lastAssistant = cur
        if (cur.role === 'assistant' && next.role === 'user') {
          const d = this.calculateReplyDelay(cur, next)
          if (d !== null) replyDelays.push(d)
        }
      }
      const lastMsg = history[history.length - 1]
      if (lastMsg.role === 'assistant') lastAssistant = lastMsg

      return {
        chatId,
        replyDelays,
        silenceDuration: this.calculateSilenceDuration(lastAssistant, currentTime),
        lastAssistantMessage: lastAssistant,
      }
    } catch (err) {
      logger.error(`[LatencyAnalyzer] 客户 ${chatId} 分析失败`, err)
      return null
    }
  }

  /* ---------- 计算辅助 ---------- */
  private static calculateReplyDelay(
    assistantMsg: IDBBaseMessage,
    userMsg: IDBBaseMessage,
  ): number | null {
    const a = dayjs(assistantMsg.created_at)
    const u = dayjs(userMsg.created_at)

    // 跨天且满足晚上发/早晨回的情况不计延迟
    if (!a.isSame(u, 'day')) {
      if (a.hour() >= 18 && u.hour() <= 12) return null
    }

    const diff = u.diff(a, 'minute')
    return diff > 0 && diff < 24 * 60 ? diff : null
  }

  private static calculateSilenceDuration(
    lastAssistant: IDBBaseMessage | undefined,
    now: Date,
  ): number {
    if (!lastAssistant) return 0
    return dayjs(now).diff(dayjs(lastAssistant.created_at), 'minute')
  }

  /* ---------- 延迟趋势分析 ---------- */
  private static analyzeDelayTrend(delays: number[]): DelayTrend {
    if (delays.length < this.MIN_DELAY_SAMPLES) return { trend: '对话轮数不足以支持延迟分析' }

    // 1) MAD 去异常值
    const clean = this.removeOutliers(delays)
    if (clean.length < this.MIN_DELAY_SAMPLES) return { trend: '对话轮数不足以支持延迟分析' }

    // 2) 线性回归 y = a + b·x
    const n = clean.length
    const xs = clean.map((_, i) => i)
    const meanX = xs.reduce((s, x) => s + x, 0) / n
    const meanY = clean.reduce((s, y) => s + y, 0) / n
    const cov = xs.reduce((s, x, i) => s + (x - meanX) * (clean[i] - meanY), 0)
    const varX = xs.reduce((s, x) => s + (x - meanX) ** 2, 0) || 1
    const slope = cov / varX
    const intercept = meanY - slope * meanX

    const ssTot = clean.reduce((s, y) => s + (y - meanY) ** 2, 0)
    const ssRes = clean.reduce((s, y, i) => s + (y - (intercept + slope * xs[i])) ** 2, 0)
    const r2 = ssTot === 0 ? 0 : 1 - ssRes / ssTot

    let trend: DelayTrend['trend']
    if (slope > this.TREND_SLOPE_THRESHOLD) trend = '回复延迟逐渐提高'
    else if (slope < -this.TREND_SLOPE_THRESHOLD) trend = '回复延迟逐渐降低'
    else trend = '回复延迟不变'

    return { trend, slope, r2, averageDelay: meanY }
  }

  /** MAD 去除异常值 */
  private static removeOutliers(delays: number[]): number[] {
    const sorted = [...delays].sort((a, b) => a - b)
    const median = sorted[Math.floor(sorted.length / 2)]
    const mad =
        sorted.reduce((sum, v) => sum + Math.abs(v - median), 0) / sorted.length || 1
    return delays.filter((v) => Math.abs(v - median) <= 3 * mad)
  }

  /* ---------- 调度决策 ---------- */
  private static shouldScheduleForSilence(
    lastAssistant: IDBBaseMessage | undefined,
    silence: number,
  ): boolean {
    if (!lastAssistant) return false
    if (!this.isExpectingReply(lastAssistant)) return false
    return silence > this.SILENCE_THRESHOLD
  }

  private static shouldScheduleForDelayTrend(trend: DelayTrend): boolean {
    if (trend.trend !== '回复延迟逐渐提高') return false
    if (!trend.slope || !trend.averageDelay) return false
    return (
      trend.averageDelay > this.REPLY_DELAY_THRESHOLD &&
        trend.slope > this.TREND_SLOPE_THRESHOLD
    )
  }

  private static isExpectingReply(message: IDBBaseMessage): boolean {
    // TODO: 根据消息特征判断是否期待回复
    return true
  }

  /* ---------- 评估并调度 ---------- */
  private static async evaluateAndScheduleMessage(
    customer: CustomerLatencyData,
    currentTime: Date,
  ): Promise<void> {
    const { chatId, replyDelays, silenceDuration, lastAssistantMessage } = customer
    const trend = this.analyzeDelayTrend(replyDelays)

    const silent = this.shouldScheduleForSilence(lastAssistantMessage, silenceDuration)
    const delayed = this.shouldScheduleForDelayTrend(trend)
    if (!silent && !delayed) return

    const reason = silent
      ? `沉默时长超过阈值: ${silenceDuration}分钟`
      : `回复延迟趋势异常: ${trend.trend}, slope=${trend.slope?.toFixed(2)}min/turn, R²=${trend.r2?.toFixed(2)}`
    logger.log(`[LatencyAnalyzer] 客户 ${chatId} 需要调度消息, 原因: ${reason}`)

    // 获取当前消息哈希用于后续检查
    const initialMessageHash = await ChatInterruptHandler.getChatVersion(chatId)

    await this.scheduleMsg(chatId, reason, { silenceDuration, trend, replyDelays, initialMessageHash })
  }

  private static async scheduleMsg(chatId: string, reason: string, data: any): Promise<void> {
    try {
      logger.log(`[LatencyAnalyzer] 开始为客户 ${chatId} 分析最佳触达策略, 原因: ${reason}`)

      // 使用 LLM 分析客户画像和对话历史，生成个性化的触达策略
      const strategy = await this.generatePersonalizedStrategy(chatId, reason, data)

      if (!strategy) {
        logger.warn(`[LatencyAnalyzer] 无法为客户 ${chatId} 生成有效的触达策略`)
        return
      }

      // 计算延迟时间（毫秒）
      const delayTime = strategy.delayMinutes * 60 * 1000

      // 使用 setTimeout 延迟发送消息
      setTimeout(async () => {
        try {
          // 在发送前再次检查是否有新消息
          const currentMessageHash = await ChatInterruptHandler.getChatVersion(chatId)
          if (currentMessageHash !== data.initialMessageHash) {
            logger.log(`[LatencyAnalyzer] 客户 ${chatId} 在等待期间有新消息，取消发送`)
            return
          }

          await this.sendPersonalizedMessage(chatId, strategy.message, reason)
          logger.log(`[LatencyAnalyzer] 已向客户 ${chatId} 发送个性化跟进消息`)
        } catch (error) {
          logger.error(`[LatencyAnalyzer] 发送消息失败 for ${chatId}:`, error)
        }
      }, delayTime)

      logger.log(`[LatencyAnalyzer] 已为客户 ${chatId} 调度个性化跟进任务, 将在 ${strategy.delayMinutes} 分钟后执行`)
    } catch (error) {
      logger.error(`[LatencyAnalyzer] 调度消息失败 for ${chatId}:`, error)
    }
  }

  /**
   * 使用 LLM 生成个性化的触达策略
   */
  private static async generatePersonalizedStrategy(
    chatId: string,
    reason: string,
    data: any
  ): Promise<PersonalizedStrategy | null> {
    try {
      const state = await getState(chatId, getUserId(chatId))

      // 构建上下文信息
      const contextBuilder = new ContextBuilder({ state })
      const customerPortrait = await contextBuilder.customerPortrait(chatId)
      const customerBehavior = await contextBuilder.customerBehavior(chatId)
      const temporalInfo = await contextBuilder.temporalInformation(chatId)

      // 获取最近的对话历史
      const recentHistory = await chatHistoryServiceClient.getChatHistory(chatId, 8, 15)

      // 构建 LLM prompt
      const prompt = this.buildStrategyPrompt(reason, data, customerPortrait, customerBehavior, temporalInfo, recentHistory)

      const llm = new LLM({
        temperature: 0.7,
        max_tokens: 800,
        meta: {
          chat_id: chatId,
          promptName: 'latency_analysis_strategy',
        },
      })

      const response = await llm.predict(prompt)

      // 解析 LLM 响应
      return this.parseStrategyResponse(response)
    } catch (error) {
      logger.error(`[LatencyAnalyzer] 生成个性化策略失败 for ${chatId}:`, error)
      return null
    }
  }

  /**
   * 构建策略分析的 prompt
   */
  private static buildStrategyPrompt(
    reason: string,
    data: any,
    customerPortrait: string,
    customerBehavior: string,
    temporalInfo: string,
    recentHistory: string
  ): string {
    return `你是一位资深的客户关系管理专家，擅长分析客户行为并制定个性化的沟通策略。

## 当前情况
${reason}

## 延迟分析数据
- 沉默时长: ${data.silenceDuration} 分钟
- 回复延迟趋势: ${data.trend?.trend || '无'}
- 平均延迟: ${data.trend?.averageDelay || '无'} 分钟
- 历史回复延迟: ${data.replyDelays?.join(', ') || '无'} 分钟

${customerPortrait}

${customerBehavior}

${temporalInfo}

${recentHistory}

## 任务要求
请基于以上信息，为这位客户制定个性化的主动触达策略：

1. **分析客户状态**: 结合客户画像、行为模式和当前时间，分析客户可能的状态和原因
2. **最佳触达时间**: 推理出对于这个客户最合适的触达时间（考虑其作息习惯、行业特点等）
3. **个性化消息**: 设计一条温暖、自然且有针对性的消息，避免机械化的模板话术

## 输出格式
请严格按照以下 XML 格式输出：

<reasoning>
[你的分析推理过程，包括对客户状态的判断、时间选择的理由等]
</reasoning>

<delay_minutes>
[建议延迟发送的分钟数，范围 5-180 分钟]
</delay_minutes>

<message>
[个性化的消息内容，要求自然、温暖、有针对性]
</message>

注意：
- 消息要体现对客户的了解和关心
- 避免过于直接的销售话术
- 考虑客户的具体情况和可能的困难
- 时间安排要合理，不要在深夜或过早打扰客户`
  }

  /**
   * 解析 LLM 策略响应
   */
  private static parseStrategyResponse(response: string): PersonalizedStrategy | null {
    try {
      const reasoning = XMLHelper.extractContent(response, 'reasoning')
      const delayMinutesStr = XMLHelper.extractContent(response, 'delay_minutes')
      const message = XMLHelper.extractContent(response, 'message')

      if (!reasoning || !delayMinutesStr || !message) {
        logger.warn('[LatencyAnalyzer] LLM 响应格式不正确:', response)
        return null
      }

      const delayMinutes = parseInt(delayMinutesStr.trim(), 10)
      if (isNaN(delayMinutes) || delayMinutes < 5 || delayMinutes > 180) {
        logger.warn('[LatencyAnalyzer] 延迟时间不合理:', delayMinutesStr)
        return null
      }

      return {
        message: message.trim(),
        delayMinutes,
        reasoning: reasoning.trim()
      }
    } catch (error) {
      logger.error('[LatencyAnalyzer] 解析策略响应失败:', error)
      return null
    }
  }

  /**
   * 发送个性化消息
   */
  public static async sendPersonalizedMessage(chatId: string, message: string, reason: string): Promise<void> {
    try {
      const userId = getUserId(chatId)
      await wecomCommonMessageSender.sendText(chatId, {
        type: SendMessageType.text,
        text: message,
        description: `延迟分析跟进: ${reason}`
      })
      logger.log(`[LatencyAnalyzer] 已向客户 ${chatId} 发送个性化消息: ${message}`)
    } catch (error) {
      logger.error(`[LatencyAnalyzer] 发送消息失败 for ${chatId}:`, error)
    }
  }
}

/*
 * 如果需要每日 3 次运行，可在启动脚本中使用 node-cron：
 * import cron from 'node-cron';
 * cron.schedule('0 10,14,18 * * *', () => LatencyAnalyzer.analyzeAndScheduleMsg());
 */
