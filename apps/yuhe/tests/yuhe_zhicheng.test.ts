import { Config } from 'config'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import dayjs from 'dayjs'
import { XingyanAPI } from 'model/xingyan'
// import { XingyanAPI } from 'model/xingyan'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('****************')
    // 判断是否志诚

    // console.log(Config.getYuHeAccountType())

    // console.log(new XingyanClient().getDefaultAccountConfig())

    // 调用接口测试下

    // await DataService.getCourseLinkByCourseNo()

    const courseDate = dayjs('********'.toString())
      .add(1, 'day')
      .format('YY/M/D')

    const data = await XingyanAPI.getRoomGroupInfo(`${courseDate}中神通`)

    console.log(data)
  })
})