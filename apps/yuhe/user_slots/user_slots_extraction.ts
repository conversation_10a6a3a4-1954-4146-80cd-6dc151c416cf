import { chatHistoryServiceClient, chatStateStoreClient } from '../service/instance'
import { BaseExtractUserSlots } from 'service/user_slots/extract_user_slots'

const TopicRecommendations = `- 基本信息：称呼、客户年龄，性别，所在城市，店面类别，行业类目，年营业额
- 抖音运营状态：是否在抖音在做，粉丝数量，账号类型，发布频率，内容质量，浏览量
- 生意现状：获课方式
- 想要解决的问题`

const TopicRules = `1. 基本信息::行业类目：需要分类为”餐饮“、”装修建材“、”服装“、”美业“、请分类为以上几个，如果属于其他分类填其他，详细的行业需要在分类之后填写在最后的括号中，如‘餐饮（烧烤摊）’“
2. 抖音运营状态::是否抖音在做：应该只分类为“是“或”否“，如遇到其他情况，应该根据含义分为”是“或”否“
3. 基本信息::店面类别：应该只分类为“个人实体店“或”连锁店“，如遇到其他情况，应该根据含义分为”个人实体店“或”连锁店“
4. 基本信息::称呼：如果客户发送的称呼是自己的人名，禁止将客户原名提取出来，你需要结合客户的性别将称呼改为姓加“哥”，姓加“姐”
5. 想要解决的问题：客户可能需要解决一些与抖音相关或自己经营店铺相关的问题
6. 不要提取手机号`



export class ExtractUserSlots extends BaseExtractUserSlots {
  constructor() {
    super(chatHistoryServiceClient, chatStateStoreClient)
  }
  getTopicRules(): string {
    return TopicRules
  }

  getTopicRecommendations(): string {
    return TopicRecommendations
  }
}