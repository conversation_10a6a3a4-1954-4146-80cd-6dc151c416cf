import { faker } from '@faker-js/faker'
import { IWorkflowState } from 'service/llm/state'
import { ChatInterruptHandler } from 'service/message_handler/interrupt/interrupt_handler'
import { CourseTimeNode } from '../course_time'
import { chatHistoryServiceClient } from '../../../service/instance'

describe('Test', function () {
  beforeAll(() => {

  })


  it('respondCourseDateTest', async () => {

    chatHistoryServiceClient.addBotMessage = async (chat_id: string, message: string, shortDes?: string, options?: any) => {
      console.log(message)
    }

    const userMessage = '什么时候上课'
    const chat_id =  faker.string.uuid()
    await chatHistoryServiceClient.addUserMessage(chat_id, userMessage)
    const dbMessages = await chatHistoryServiceClient.getChatHistoryByChatId(chat_id)

    const state:IWorkflowState =  {
      chat_id,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chat_id)
    }

    await CourseTimeNode.invoke(state)
  }, 9E8)
})