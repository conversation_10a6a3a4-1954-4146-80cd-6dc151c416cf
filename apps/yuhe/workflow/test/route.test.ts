import { getUserId } from 'config/chat_id'
import { JuziAP<PERSON> } from 'model/juzi/api'
import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { EventHandler } from '../../client/event_handler'
import { checkRobotDetection } from 'service/agent/utils'
import { PrismaMongoClient } from '../../database/prisma'
import { chatStateStoreClient, humanTransferClient } from '../../service/instance'

describe('Test', function () {
  beforeAll(() => {

  })

  it('checkRobotDetection', async () => {
    const mongoClient = PrismaMongoClient.getInstance()
    const chat_id = '7881302298050442_1688857404698934'
    const user_id = getUserId(chat_id)
    const userMessage = '老师，你他妈就是AI吧'
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chat_id, '', user_id, userMessage)
    console.log('isRobotDetection', isRobotDetection)
  }, 60000)

  it('test invite to group', async () => {
    JuziAPI.wxIdToExternalUserId = async (user_id) => {
      return '11'
    }
    Config.setting.wechatConfig = await loadConfigByAccountName('yuhe1')
    await EventHandler.inviteToGroup('7881302298050442_1688857404698934', '7881302298050442_1688857404698934')
  }, 60000)
})
