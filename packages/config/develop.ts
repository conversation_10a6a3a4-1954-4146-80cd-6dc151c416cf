import { IDevelopConfig, IModelConfig } from './interface'

export const DevelopModelConfigure: IModelConfig = {
  openai: {
    apiKeys: ['sk-free-spirit-baoshu-afjadH7CkZinpkiDjqfxT3BlbkFJGym4WWRVmLptQE0jsdTc'],
    apiBaseUrl: 'http://47.74.9.5/v1'
  },

  azureOpenAI: {
    azureOpenAIApiKey: '********************************',
    azureOpenAIApiVersion: '2024-12-01-preview',
    azureOpenAIApiInstanceName: 'top-sales',
    azureOpenAIApiDeploymentName: ['gpt-4.1', 'gpt-4.1-mini', 'o4-mini'],
    apiBaseUrl: 'https://top-sales.openai.azure.com/'
  },

  cheapOpenAI: {
    apiKey: 'sk-acZbQHrU7Cjpao3lNyoHvpHeQ6nJiuBUoqU0FtMJ2rJeoWAi',
    apiBaseUrl: 'https://api.kksj.org/v1'
  },

  qwen: {
    apiKey: 'sk-bfVi9qWvQ6',
  },

  stableClaude: {
    apiKey: 'sk-bYHUsnmDsYuKBHpT5eD2AbF126564251B798937bE99168F6',
    apiBaseUrl: 'https://api.oaipro.com/v1'
  }
}

export const DevelopConfigure: IDevelopConfig = {
  ...DevelopModelConfigure,

  polyv: {
    appId: 'guacx10u3c',
    userId: '3ea4e7bdfb',
    appSecret: '233f734ebf7248dfb0f974779b3db552'
  },

  xbb: {
    token: '8b1dea73ba532249dc5ace6a4b3f5d6b'
  },

  baoshuConfig: {
    operationGroupId: ''
  },

  xunfeiASR: {
    appId: '3cf5d032',
    secretKey: 'f5ad37355aab726d0b8ef4dbb47f6856'
  },

  bingSearch: {
    apiKey: '********************************'
  },

  elasticSearch: {
    url: 'http://es-cn-84a3u6uoq0004wu11.public.elasticsearch.aliyuncs.com:9200',
    username: 'elastic',
    password: 'free1234$spirit!'
  },

  langsmith: {
    apiKey: '***************************************************'
  },

  waitingTime: {
    messageMerge: 15 * 1000, // 合并消息等待时间


    activePush: {
      interval: 6 * 1000, // 主动推送间隔
    },

    reAsk: {
      firstWaitingTime: 30 * 1000, // 初次追问等待时间
      pushGroup: 60 * 1000, // 追问进群
      groupLinkNotClicked: 60 * 1000, // 进群链接未点击
    },

  },


  messageCenter: {
    url: 'http://118.31.69.106:8000',
  },

  apiCenter: {
    agentBaseUrl: 'https://agent-chatbot-service-ogktquvwrg.cn-hangzhou.fcapp.run'
  },

  oss: {
    static: {
      bucket: 'free-spirit-static',
      internal: false,
      region: 'oss-cn-hangzhou',
      domain: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/',
    },
  },

  sls: {
    account: {
      accessKeyId: 'LTAI5t7V4xoVdFrqezLZfD6P',
      accessKeySecret: '******************************',
    },

    tokenApi: {
      endpoint: 'http://nls-meta.cn-shanghai.aliyuncs.com',
      apiVersion: '2019-02-28',
    },

    speechRecognition: { //一句话识别
      url: 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1',
      appKey: 'ZZSreJt1mPevUsYA',
    }
  },

  mongoDB: {
    wechat_db : {
      uri: 'mongodb://root:free1234$spirit!@dds-bp182d578f1886441663-pub.mongodb.rds.aliyuncs.com:3717,dds-bp182d578f1886442182-pub.mongodb.rds.aliyuncs.com:3717/free_spirit?authSource=admin'
    },
    log_db: {
      uri: 'mongodb://root:free1234$spirit!@dds-bp183f68818ca1d4-pub.mongodb.rds.aliyuncs.com:3717/admin'
    }
  },

  redis: {
    url: 'r-uf6xk3zjd1mzs4540dpd.redis.rds.aliyuncs.com',
    password: 'free1234$spirit!'
  },

  juziWecom: {
    baseUrl: 'https://ah-bg.ddregion.com/hub-api/api/',
    token: 'e0d70927040a4efa92b79b7279ecb1c1'
  },

  yiwise: {
    appKey: 'JkeQ0mwOT4r9wmFa',
    appSecret: 'DXnpuOQzq3AAXTEZoOlu8i80QJaHaz1c',
    tenantSign: 'moerzhihui',
    version: 'v1'
  },

  moerWxBiz: {
    corpid: 'wwb240d4bb3b5a663d',
    corpsecret: 'z5PJQdvinKUchssNTTNSI3pz8UvauhaZdoriEjOWu_0'
  },

  siliconFlow: {
    apiKey: 'sk-kzrslizeehyrpccsvpafohnpstbbdcvjlbxjojalufszxqdw'
  },

  xingyan: {
    baseUrl: 'https://open-api.marscrm.cn/openApi',
    zhongshentong: {
      cropId: ****************,
      secretKey: '0dd9889bb44646c072a0bb8d44002d9b'
    },
    zhicheng: {
      cropId: ****************,
      secretKey: '733d96254487afbbb8892cdfd3b82e61'
    }
  },

  whatsapp: {
    baseUrl: 'https://group.dispatch.channelepoch.com/',
    apiKey: '92160aea1c817da65669c4df75e867f7',
    tenantId: '544999',
    mainAccountUserName: 'jiuzai'
  },
}
