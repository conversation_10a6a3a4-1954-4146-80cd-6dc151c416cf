import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'
import { Config } from '../../../config'

interface IOpenAIInitParams {
  model?: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT = 2 * 60 * 1000  // 2 minutes

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-4.1',
      temperature = 0,
      maxTokens = 1024,
      topP = 0.9,
      frequencyPenalty = 0.5,
      timeout = CLIENT_DEFAULT_TIMEOUT
    } = params

    if (timeout < 1000) { throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时') }

    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      maxTokens: maxTokens,
      topP: topP,
      frequencyPenalty: frequencyPenalty,
      timeout: timeout,
      openAIApiKey: Config.setting.openai.apiKeys[0],
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
      maxRetries: 1
    })
  }
}

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const {
      model = 'gpt-4.1',
      temperature = 0,
      maxTokens = 1024,
      topP = 0.9,
      frequencyPenalty = 0.5,
      timeout = CLIENT_DEFAULT_TIMEOUT
    } = params

    if (timeout < 1000) { throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时') }

    let selectedModel = 'gpt-4.1' // Azure 部署名称
    if (model.includes('gpt-4.1-mini')) { selectedModel = 'gpt-4.1-mini'
    } else if (model.includes('o4-mini')) { selectedModel = 'o4-mini' }

    const azureConfig: any = {
      temperature: temperature,
      maxTokens: maxTokens,
      topP: topP,
      model: selectedModel,
      frequencyPenalty: frequencyPenalty,
      timeout: timeout,
      azureOpenAIApiDeploymentName: selectedModel,
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      configuration: { baseURL: Config.setting.azureOpenAI.apiBaseUrl },
      maxRetries: 1
    }

    if (azureConfig.azureOpenAIApiDeploymentName.startsWith('o')) {
      delete azureConfig.maxTokens
      delete azureConfig.temperature
      delete azureConfig.topP
      delete azureConfig.frequencyPenalty
      azureConfig.model_kwargs = { 'max_completion_tokens': maxTokens }
    }

    return new AzureChatOpenAI(azureConfig)
  }
}

export class CheapOpenAI {
  public static getClient(model = 'gpt-4.1', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: Config.setting.cheapOpenAI.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}

export class StableClaude {
  public static getClient(model = 'claude-3-5-sonnet-20241022', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1,
      openAIApiKey: Config.setting.stableClaude.apiKey,
      configuration: {
        baseURL: Config.setting.stableClaude.apiBaseUrl
      },
    })
  }
}

export class MiTaAI {
  public static getClient(model: 'concise' | 'detail' | 'research', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
    })
  }
}

export class QwenMax {
  public static getClient(temperature = 0) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient(temperature = 0) {
    return new ChatOpenAI({
      model: 'llama-3-sonar-large-32k-online',
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
    })
  }
}