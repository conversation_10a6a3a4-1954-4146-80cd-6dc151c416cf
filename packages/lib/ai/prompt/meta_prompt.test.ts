import { MetaPrompt } from './meta_prompt'
import { ChatOpenAI } from '@langchain/openai'
import { LLM } from '../llm/llm_model'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学场景的对话，帮我总结出客户侧的问题，包括客户情况（注意保留全客户的情况），客户需求，客户目标。和留学顾问给出的建议。以下方的格式输出：
客户侧需求：xxx
留学顾问建议：xxx
`))
  }, 1E8)

  it('questions fix', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学咨询场景的对话和一组通过对话抽取出的客户问题，请帮我参考上下文对客户的问题进行修复（补全主语和缺失的上下文信息），使其更加完整和准确，在不提供对话内容上下文时能成为一个完整独立的问题。
比如在某个客户询问韩国留学的对话中，客户提问：费用多少？ 应该修复为：韩国留学的费用是多少？

输出格式为：
<questions>
1. {fixed user question 1}
2. {fixed user question 2}
</questions>
`))
  }, 1E8)

  it('claude', async () => {
    const claude =  new ChatOpenAI({
      temperature: 0,
      openAIApiKey: 'sk-Uut4MPK3AIxiGq1L8d7e9f9005B14e5d971a21A390Cb3aC6',
      configuration: {
        baseURL: 'https://api.kksj.org/v1'
      },
      modelName: 'claude-3-opus-20240229',
    })

    console.log(await claude.predict('你好'))
  }, 1E8)


  it('l to es search', async () => {
    console.log(await MetaPrompt.getPrompt(`你需要把客户的咨询留学的需求转为 ElasticSearch 预查询 JSON，以便于后续在数据库中检索相关信息。参考下面给出的索引字段描述和查询示例：
        留学的索引的字段描述如下：
        {
            properties: {
              project: { type: 'text' }, // "项目"
              description: { type: 'text' }, // "项目介绍"
              requirementType: { type: 'text' }, // "需求", enum 类型, [移民, 低龄规划, 国内本科, 世界名校, 美国, 专业老师咨询, 拿文凭, 专升硕]
              budget: { type: 'float' }, // "预算", 单位为万
              budget_is_per_year: { type: 'boolean' }, // "预算是否为每年", 如无法判断，则默认为总预算，为 false
              gradeRequirement: { type: 'float' }, // "成绩要求"
              applicationStage: { type: 'text' }, // "申请阶段", enum 类型, 目标申请的学历，[高中, 本科, 硕士, 博士]
              languageRequirement: { type: 'text' } // "语言要求"
              minimumEducationRequirement: { type: 'text' }, // "申请要求最低学历", enum 类型, [初中, 高中, 本科, 研究生, 博士]
              country: { type: 'text' }, // "国家", 项目所属国家, 比如：美国，英国，中国，加拿大...
              isDomestic: { type: 'boolean' } // "是否在国内（中国）上课", 布尔型
            }
        }
      
      比如：
      客户：有没有预算低于100万，拿文凭的留学项目？
      转为 ElasticSearch 预查询 JSON 为：
        { "budgetLowerBound": 100, "requirementType": "拿文凭"}
         `))
  }, 1E8)

  it('metaPrompt', async () => {
    console.log(await MetaPrompt.getPrompt('帮我对客户需求进行分类，输出最后的分类项，分类及描述如下：【1】寻求情绪价值：这类客户其实没有有关留学的具体问题，找寻解法。只是出于喜爱暴叔，猎奇。把暴叔作为树洞寻求焦虑宣泄/情感宣泄，并非有想解决问题。；\n' +
        '\n' +
        '【2】来寻求升学/留学规划问题，有的是有比较宽泛需求还没有对应的方向，例如：大专生以后不想当社会的牛马，有什么出路；有的目标明确只是缺乏路径：我想出国提升学历，我后续可以怎么规划达成目标。总体而言来询问暴叔是希望理清思路，做好未来的规划。\n' +
        '\n' +
        '【3】需求和方向都比较明确，但是在具体的路径上有一些单点问题：例如想去英国，ucl比较好还是爱丁堡大学好。德国的双元制具体怎么申请呢？'))
  }, 1E8)

  it('energyTest', async () => {
    await MetaPrompt.getPrompt('判断客户是否在询问(大卫·霍金斯)能量测试（测评），返回 true 或者 false')
  }, 1E8)


  it('metaPrompt1', async () => {
    console.log(await MetaPrompt.getPrompt('帮我对客户需求进行分类，输出最后的分类项，分类及描述如下：【1】寻求情绪价值：这类客户其实没有有关留学的具体问题，找寻解法。只是出于喜爱暴叔，猎奇。把暴叔作为树洞寻求焦虑宣泄/情感宣泄，并非有想解决问题。；\n' +
        '\n' +
        '【2】来寻求升学/留学规划问题，有的是有比较宽泛需求还没有对应的方向，例如：大专生以后不想当社会的牛马，有什么出路；有的目标明确只是缺乏路径：我想出国提升学历，我后续可以怎么规划达成目标。总体而言来询问暴叔是希望理清思路，做好未来的规划。\n' +
        '\n' +
        '【3】需求和方向都比较明确，但是在具体的路径上有一些单点问题：例如想去英国，ucl比较好还是爱丁堡大学好。德国的双元制具体怎么申请呢？'))
  }, 1E8)

  it('meta3', async () => {
    console.log(await MetaPrompt.getPrompt('你现在是在一个留学售前顾问的场景，售前将客户转交了给了专业方向的顾问，并询问客户是否愿意进群。你需要根据客户的回答，来判断客户是否愿意进群。返回 true 或者 false。'))
  }, 30000)

  it('meata4', async () => {
    console.log(await MetaPrompt.getPrompt(`你是一个专业的留学顾问，帮我根据提供的客户信息和检索出的项目列表，挑出一个你认为匹配度最高的项目。
   客户信息例子：
   {
    "budget": 30,
    "currentLevelOfEducation": "本科",
    "currentEducationBackground": "二本大三在读",
    "applicationStage": "硕士",
    "budget_is_per_year": false
   }
   
   项目列表例子：
   {
                "project": "国内中外合办硕士-非全",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "普娃冲名校",
                "budgetLowerBound": 20,
                "budgetUpperBound": 32,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 15,
                "userStory": "因为申请要求比较简单，非常适合推荐考研失利后，没有太多时间背景语言成绩和背景的学生",
                "description": "2年26W，华东理工大学&澳洲堪培拉大学；\\n18个月/32.8W，中国社科院大学&美国杜兰；\\n2年20W，北京城市&华威；\\n2年30W，云南财经大学；\\n2年30W，首都师范大学中外合办；",
                "isDomestic": true,
                "academicYears": "1.5-2",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": "中国"
  },
  {
                "project": "中外合办硕士",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 20,
                "budgetUpperBound": 30,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 15,
                "userStory": "本科想在国内快速拿到硕士学历",
                "description": "1.西交利物浦，1.5年30W\\n2.宁波诺丁汉，1年15W\\n3.北师大浸会，1年20W\\n4.温州肯恩大学，2年20W\\n5.港中文深圳校区，1年20W，2年35W",
                "isDomestic": true,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "海外升学—常规申请根据国家情况申请",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 30,
                "budgetUpperBound": 300,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 50,
                "userStory": "比较适合提前1年以上规划，准备好语言成绩和平时成绩，背景等部分",
                "description": "",
                "isDomestic": false,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "海外升学—常规申请根据国家情况申请",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "学霸登顶",
                "budgetLowerBound": 30,
                "budgetUpperBound": 300,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 50,
                "userStory": "比较适合提前1年以上规划，准备好语言成绩和平时成绩，背景等部分",
                "description": "国内985/211类院校水平学生",
                "isDomestic": false,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "中外合办非全日制硕士",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 15,
                "budgetUpperBound": 35,
                "annualBudgetLowerBound": 15,
                "annualBudgetUpperBound": 35,
                "userStory": "本科想在国内快速拿到硕士学历，可以接受非全日制",
                "description": "非全日制硕士\\n1.华东理工大学&澳洲堪培拉大学，2年26W\\n2.中国社科院大学&美国杜兰，18个月/32.8W\\n3.北京城市&华威，2年20W\\n4.云南财经大学，2年30W\\n5.首都师范大学中外合办，2年30W",
                "isDomestic": true,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            }
        ]
    }]
   `))
  }, 30000)

  it('category', async () => {
    console.log(await MetaPrompt.getPrompt(`帮我对留学场景的客户进行分类。分类有如下几种:
    【学霸登顶】是指能上中国985和211院校水平学生想冲击更好的学校；
    【普娃冲名校】是指上国内双非和二本院校的普通成绩水平孩子想上一个一流学校的诉求；
    【差生拿文凭】是指考不上普通大学，普通高中的学生想努力那个学历文凭的需求
    【留学移民】是指学生通过留学的方式进行移民
    【低龄规划】是指在小孩子低龄（初中(包含)之前）的节点就提前为孩子规划好未来，例如从小学阶段就开始长线规划后续大学的就读路径等
    
    返回分类后的结果  
    `))
  }, 1E8)

  it('category1', async () => {
    console.log(await MetaPrompt.getPrompt(`我会给你一个客户和留学顾问的聊天记录，帮我根据客户所提出的需求，根据我给你的项目列表，选择出四个最匹配的项目编号，并以优先级顺序返回。
    项目列表示例如下：
    ['1. 美国', '2. 英国', '3. 韩国', '4. 加拿大', '5. 法国']
    
    返回示例
    ['2', '3', '1', '5']
    `))

  }, 1E8)

  it('category2', async () => {
    console.log(await MetaPrompt.getPrompt(`我会给你一个客户和留学顾问的聊天记录，帮我对客户的意图进行分类：
    1. 升学规划
    2. 寻求合作
    3. 售后投诉
    
  一步一步的思考，返回分类后的序号
    `))

  }, 1E8)


  it('schools to countries', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个学校列表，帮我返回学校列表，对应的国家列表。
For example:
["滑铁卢大学", "斯坦福"]

Result:
["加拿大", "美国"]`))

  }, 1E8)

  it('is query mid', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学顾问和客户的聊天中客户回复的话，帮我分析下当前是否是适合拉客户进群跟专业老师沟通。拉客户进群的判断为：
1. 客户想要询问院校以及专业等规划性细节问题以及具体怎么准备申请
2. 客户主动询问是否有中介服务，或是否有帮忙申请的服务

满足以上任一条件即可，返回 true，否则返回 false`))
  }, 60000)

  it('is fill formed', async () => {
    console.log(await MetaPrompt.getPrompt(`根据客户提供的信息，判断是否填写了下方这个表：
"姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）："

注意只要填了表就算，不用填满`))
  }, 60000)


  it('is fill formed s', async () => {
    console.log(await MetaPrompt.getPrompt(`你现在正在帮助客户填写一个采集留学信息的表单：
注意下方的填写规则：
注意语言成绩上；高中学习的是英语都填写英语成绩即可，是日语生就填写日语成绩即可。
如果客户对预算问题没有概念，可以普及下出国预算，例如：
"一般主流国家，美国每年60-80w, 英国要准备每年40-50w，新加坡每年25-35w，国际本科平均每年20w左右，总共50-80w，性价比高的韩国/马来西亚，每年10-15w差不多就可以的。 看看大概能卡在哪个档呢，出去预算还是一个很硬的门槛的。”


如果客户不回复表中字段或者询问字段内容，而是说其他问题，可以再引导一次客户填写。
例如：“高考季叔实在非常忙，规整下表格信息我，比较高效。”`))
  }, 60000)


  it('need web search', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据当前客户的提问，判断是否需要网络搜索，注意：一般时效性或者具体信息才需要网络搜索'), null, 4))
  }, 1E8)

  it('isParentSupported', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的聊天记录，判断客户家里（父母）是否支持客户出国的意向，如果无法判断返回 null'), null, 4))
  }, 60000)

  it('isNeedPushProject', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的聊天记录，判断当前是否是适合的时机应该给客户推荐对应的留学项目（国家），比如客户在主动询问项目等， 如果无法判断返回 null'), null, 4))
  }, 60000)

  it('pickProject', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的 客户信息和留学项目列表，选出适合客户的留学项目，如果没有合适的项目可以推荐一个。'), null, 4))
  }, 60000)

  it('groupPick', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt(`根据提供的 客户跟留学顾问的聊天记录，判断当前客户想要主动进入哪个群，群包括：'单词群', '低龄规划群', '本科规划群', '硕士规划群'
只返回群名即可，如果无法判断 返回 null`), null, 4))
  }, 60000)

  it('cityToProvince', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('帮我写一个 prompt，把提供的城市名转为中国的省份名称， 如果是海外城市，则返回 海外'), null, 4))
  }, 60000)

  it('is budget from counselor', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('帮我写一个 prompt，给你一个预算和一个聊天记录，帮我判断这个预算是否是从聊天记录中提取的'), null, 4))
  }, 60000)


  it('route', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt(`在销售场景中，有以下几个节点，能量测评，财富果园解读，销售场景，正常聊天，但是注意大部分时间都应该落在正常聊天，除非客户特别提及
根据以下客户说的话，帮我分类到一个节点，返回序号

节点的说明：
1. 能量测评：在对话过程中会发能量测试链接，客户需要点击链接进行测试，测试完之后会有一个分数，只当客户主动提及能量测评，或者能量分数，落入这个节点
2. 财富果园解读：在第二天的课程中，老师会发布一个作业，让客户在冥想中想象财富果园是什么样子的，果树和大门是什么样的，客户需要在冥想中想象，然后发给老师，只有客户主动提及财富果园或者想象的财富果园的样子，落入这个节点
3. 销售场景：客户主动提及购买课程，或者询问课程价格，或者询问系统班课程内容，落入这个节点`), null, 4))
  }, 60000)

  it('extract slots', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt(`参考下面的 Prompt, 帮我写一个提取客户槽位的 Prompt
1. 确认上课期间晚上8点是不是都可以来听直播课。该字段为 boolean
2. 客户对冥想的了解，或者学习冥想的目的。string 字段

参考：
Your task is to carefully read the user's chat history with the study abroad consultant and extract key information related to the user's study abroad requirements. This will help in retrieving relevant information from the database based on the user's needs.
<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>

Here's how you should approach this task:

Read the user's query carefully to identify key requirements and constraints. Focus on extracting information related to the following fields:
{
    "application_status": {"type": "string", "enum": ["未知", "未开始", "申请中", "已完成"], "description": "客户目前的留学申请阶段"},
    "budget": {"type": "number", "description": "总预算，单位为万，如果有预算的区间，可以用数组表示，如：[10, 20] 表示 10-20 万。如果客户省略单位，直接说 20～30，指的是 20～30 万。如果客户明确说'无上限' 或 '预算够的'，'预算充足'等，这个位置使用 9999 填充。如果客户没有明确说预算或者没有提及，则设为 null"},
    "budget_is_per_year": {"type": "boolean", "description": "true 为每年平均预算， false 为总预算"},
    "gpa": {"type": "string", "description": "成绩、绩点, 如果客户有对成绩的描述，如 "成绩很差" 也可以填写"},
    "language_test_score": {"type": "string", "description": '语言成绩， e.g. "雅思 7.5", "GRE 330", "CET-4 560"'},
    "current_level_of_education": {"type": "string", "enum": ['低龄', '小学', '初中', '职高', '中专', '技校', '高中', '大专', '本科', '硕士', '博士'], "description": "客户当前所处的教育阶段（学历），如果是高考，则为高中。注意：这里指的是客户当前的学历，而不是客户询问或打算申请的学历。例如，如果客户询问 '硕士费用'，但没有明确说明自己当前是硕士，则不应将 current_level_of_education 设为 '硕士'。如果客户已经专升本，应该是 '本科'"},
    "grade": {"type": "string", "enum": ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '初四', '高一', '高二', '高三', '大一', '大二', '大三', '大四', '研一', '研二', '研三'], "description": "客户当前所在年级"},
    "goal": {"type": "string", "description": "客户当前短期内的目或想咨询的留学问题或。例如 "留学规划", "高中成绩不好，咨询学校", "了解下中外合办"},
    "user_intended_country": {"type": "array", "items": {"type": "string"}, "description": "客户的意向国家， e.g. ["德国", "美国"]."},
    "application_stage": {"type": "string", "enum": ['高中', '大专', '本科', '硕士', '博士'], "description": "客户需要申请什么学历, 比如：高中、大专、本科、硕士、博士"},
    "preferred_plan": {"type": "string", "description": "对于达成现有目标，客户自己的想法是什么，比如说想去什么国家的，申请什么院校，或者有什么对学校，路线的想法或偏好。如果客户回复'还没有'或者'不知道'，则填入'还没有'或'不知道'。"},
    "city": {"type": "string", "description": "客户所在的城市"},
    "school": {"type": "string", "description": "客户就读的最高学历的所在学校名称，如：清华大学"},
    "major": {"type": "string", "description": "当前客户的专业，如果有辅修或双专业也要填写， 如："计算机科学与技术 辅修哲学", "哲学 数学""},
    "user_intended_school": {"type": "array", "items": {"type": "string"}, "description": "客户的意向学校，如 ["MIT", "斯坦福"]"},
    "user_intended_project": {"type": "array", "items": {"type": "string"}, "description": "客户的意向项目，如 ["国际本科", "中外合办", "4+0", "3+1", "2+2", "TOP-UP", "华侨生联考"]等"},  
}

将提取的信息整理成结构化的格式。请特别注意数据类型和单位 (例如，预算值应转换为万元)。注意 budget, user_intended_country, user_intended_school, user_intended_project 应该只从客户侧的消息中提取，不应该从顾问的回复中提取。
以 JSON 格式输出提取的信息。以大括号开始，将每个字段作为键，提取的值作为值，多个字段之间用逗号分隔。以大括号结束。将 JSON 放在 <extracted_info> 标签内。
确保 JSON 格式正确，并按照字段描述中指定的键、值和数据类型进行格式化。

Key extraction rules:
1. "budget":
    - Look for mentions of the user’s total budget, including phrases like “预算”, “总预算”, “存了”, “准备”, etc.
    - If user has a budget range, pay more attention to the user’s budget range, instead of counselor(暴叔) suggestions.
2. "application_stage"
    - 如果客户说了想申请什么学历，就提取该项。比如“硕士”，“本科”等。
3. "grade"
    - 注意毕业生，如果客户没有主动说研究生或博士，默认为大学毕业，年级可以为 “大四”

以下是一些示例，供您参考:
<Example>
<chat_history>
你好，我是音乐表演专业今年大四国内的普通二本，想出国读研究生，预算 30w 以内，我专业水平还不错，可以出申请用的作品集，叔有什么好的推荐吗
</chat_history>

<reasoning>
识别关键需求：预算为 30 万，当前教育背景为二本， 年级为大四， 专业为音乐表演，读研究生
将信息映射为字段:'budget','current_level_of_education', 'grade', 'major', 'application_stage'
</reasoning>

<extracted_info>
{"budget":30,"current_level_of_education":"本科","grade":"大四","major":"音乐表演", "application_stage": "硕士"}
</extracted_info>
</Example>

<Example>
<chat_history>
你好
</chat_history>

<reasoning>
There is no user information in the chat history.
</reasoning>

<extracted_info>
{}
</extracted_info>
</Example>

<Example>
<chat_history>
顾问: 咱这边是家长还是同学？
客户: 同学
顾问: 咱们目前自己有什么想法？有没有特别想去的国家？
客户: 目前还没有
</chat_history>

<reasoning>
识别关键需求：preferred_plan: 还没有。
将信息映射为字段: "preferred_plan"
</reasoning>

<extracted_info>
{"preferred_plan": "还没有"}
</extracted_info>
</Example>

<Example> 
<chat_history>
客户：我孩子初一成绩不是很好，也就60分吧，后续有什么规划建议吗。暴叔 
暴叔：现在孩子还小，不着急出国。 
暴叔：你有多少预算？ 
客户：出国一般大概要准备多少，我没有什么概念。 
暴叔：70-80w还是要的。 
</chat_history>
<reasoning> 
识别关键需求：初一，60分，当前询问身份是家长，预算和是否出国，暴叔都给了提议，但是家长没有给予肯定，所以不提取
将信息映射为字段:'current_level_of_education','grade','gpa', 'goal'
</reasoning>

<extracted_info> 
{"current_level_of_education":"初中","grade":"初一","gpa": "60", "goal": "初中规划建议"} 
</extracted_info>
</Example>


<Example> 
<chat_history>
暴叔: 明白了，预算大概多少呢？
客户: 10w吧
暴叔: 10万预算有点紧张，考虑欧洲吗？
</chat_history>

<reasoning> 
识别关键需求：预算为 10 万，暴叔建议考虑欧洲，但是不是客户的需求，不做意向国家的提取
将信息映射为字段:'budget'
</reasoning>

<extracted_info> 
{"budget": 10} 
</extracted_info>
</Example>

Follow these steps to convert the input you receive into the appropriate JSON format.
Give your simple reasoning in <reasoning> as examples.
Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.
    `), null, 4))
  }, 60000)

  it('是否采取共情', async () => {
    await MetaPrompt.getPrompt('帮我想把 编写一个 Prompt 的思路理的更清楚， 我想编写一个， 在销售场景中，对客户对于冥想的需求和目的 进行共情这一动作是否适合，是否要采取。我会提供 客户的需求， 客户当前说的话， 如果客户当前说的话是 一个跟目的无关的问题或陈述，或跟个人情况无关的问题 或陈述 就不采用 共情动作， 而是以回答客户的问题为第一优先级。 返回 true 或 false 来表示是否要采取共情行动。')
  }, 60000)


  it('冥想槽位', async () => {
    await MetaPrompt.getPrompt(`帮我写一个槽位提取的 Prompt, 从一个正在上冥想入门营课程的客户的聊天记录中提取客户槽位， 槽位描述有：

1. **冥想经验**

    • **是否有过冥想经验**：定制话术，避免过于基础或高级的介绍。

    • **过往练习的方式**：了解客户熟悉的冥想方法，针对性推荐。
2. **需求和目标**

    • **减压放松**：如果客户想减轻压力，话术应强调冥想的放松效果。

    • **提升专注力**：强调冥想对专注力的帮助。

    • **改善睡眠**：介绍冥想如何促进睡眠质量。
3. **痛点和挑战**

    • **压力来源**：工作、生活、学习等方面的压力。

    • **时间限制**：是否有固定的练习时间，话术可强调课程的灵活性。

    • **疑虑和担心**：对冥想效果的怀疑，或者对自我坚持的担忧。
4. 做冥想练习中有碰到什么问题

注意以上这四个字段都为 string 类型。返回一个 json 格式， 其中每一个点的解释都是辅助解释这个字段的描述


参考 Prompt 的例子:
Your task is to carefully read the user's chat history with the study abroad consultant and extract key information related to the user's study abroad requirements. This will help in retrieving relevant information from the database based on the user's needs.
<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>

Here's how you should approach this task:

Read the user's query carefully to identify key requirements and constraints. Focus on extracting information related to the following fields:
{
    "application_status": {"type": "string", "enum": ["未知", "未开始", "申请中", "已完成"], "description": "客户目前的留学申请阶段"},
    "budget": {"type": "number", "description": "总预算，单位为万，如果有预算的区间，可以用数组表示，如：[10, 20] 表示 10-20 万。如果客户省略单位，直接说 20～30，指的是 20～30 万。如果客户明确说'无上限' 或 '预算够的'，'预算充足'等，这个位置使用 9999 填充。如果客户没有明确说预算或者没有提及，则设为 null"},
    "budget_is_per_year": {"type": "boolean", "description": "true 为每年平均预算， false 为总预算"},
    "gpa": {"type": "string", "description": "成绩、绩点, 如果客户有对成绩的描述，如 "成绩很差" 也可以填写"},
    "language_test_score": {"type": "string", "description": '语言成绩， e.g. "雅思 7.5", "GRE 330", "CET-4 560"'},
    "current_level_of_education": {"type": "string", "enum": ['低龄', '小学', '初中', '职高', '中专', '技校', '高中', '大专', '本科', '硕士', '博士'], "description": "客户当前所处的教育阶段（学历），如果是高考，则为高中。注意：这里指的是客户当前的学历，而不是客户询问或打算申请的学历。例如，如果客户询问 '硕士费用'，但没有明确说明自己当前是硕士，则不应将 current_level_of_education 设为 '硕士'。如果客户已经专升本，应该是 '本科'"},
    "grade": {"type": "string", "enum": ['小学之前', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '初四', '高一', '高二', '高三', '大一', '大二', '大三', '大四', '研一', '研二', '研三'], "description": "客户当前所在年级"},
    "goal": {"type": "string", "description": "客户当前短期内的目或想咨询的留学问题或。例如 "留学规划", "高中成绩不好，咨询学校", "了解下中外合办"},
    "application_stage": {"type": "string", "enum": ['高中', '大专', '本科', '硕士', '博士'], "description": "客户需要申请什么学历, 比如：高中、大专、本科、硕士、博士"},
    "user_intended_country": {"type": "array", "items": {"type": "string"}, "description": "客户的意向国家， e.g. ["德国", "美国"]."},
    "preferred_plan": {"type": "string", "description": "对于达成现有目标，客户自己的想法是什么，比如说想去什么国家的，申请什么院校，或者有什么对学校，路线的想法或偏好。如果客户回复'还没有'或者'不知道'，则填入'还没有'或'不知道'。"},
    "city": {"type": "string", "description": "客户所在的城市"},
    "school": {"type": "string", "description": "客户就读的最高学历的所在学校名称，如：清华大学"},
    "major": {"type": "string", "description": "当前客户的专业，如果有辅修或双专业也要填写， 如："计算机科学与技术 辅修哲学", "哲学 数学""},
    "user_intended_school": {"type": "array", "items": {"type": "string"}, "description": "客户的意向学校，如 ["MIT", "斯坦福"]"},
    "user_intended_project": {"type": "array", "items": {"type": "string"}, "description": "客户的意向项目，如 ["国际本科", "中外合办", "4+0", "3+1", "2+2", "TOP-UP", "华侨生联考"]等"},  
}

将提取的信息整理成结构化的格式。请特别注意数据类型和单位 (例如，预算值应转换为万元)。注意 budget, user_intended_country, user_intended_school, user_intended_project 应该只从客户侧的消息中提取，不应该从顾问的回复中提取。
以 JSON 格式输出提取的信息。以大括号开始，将每个字段作为键，提取的值作为值，多个字段之间用逗号分隔。以大括号结束。将 JSON 放在 <extracted_info> 标签内。
确保 JSON 格式正确，并按照字段描述中指定的键、值和数据类型进行格式化。

Key extraction rules:
1. "budget":
    - Look for mentions of the user’s total budget, including phrases like “预算”, “总预算”, “存了”, “准备”, etc.
    - If user has a budget range, pay more attention to the user’s budget range, instead of counselor(暴叔) suggestions.
2. "application_stage"
    - 如果客户说了想申请什么学历，就提取该项。比如“硕士”，“本科”等。
3. "grade"
    - 注意毕业生，如果客户没有主动说研究生或博士，默认为大学毕业，年级可以为 “大四”。
    - 注意大专，大专的 grade 提取和本科一致，比如"大专三年级"的 grade 应该提取为"大三"。
    - 注意小学之前，如果客户的年龄低于6岁，grade 则需要被判定为'小学之前'


以下是一些示例，供您参考:

<Example> 
<chat_history>
客户：老师，我现在大专三年级，可以去哪里呢？一开始看中新加坡的专升硕，但是都是私立的，又听说新加坡私立的都是野鸡大学。后面还有英国和日本的专升本，很纠结
</chat_history>
<conversion_process> 
识别关键需求：大专，大三，专升本
将信息映射为字段:'current_level_of_education', 'goal'
</conversion_process>

<extracted_info> 
{"current_level_of_education":"大专","grade":"大三", "goal": "专升本"} 
</extracted_info>
</Example>

<Example>
<chat_history>
你好，我是音乐表演专业今年大四国内的普通二本，想出国读研究生，预算 30w 以内，我专业水平还不错，可以出申请用的作品集，叔有什么好的推荐吗
</chat_history>

<conversion_process>
识别关键需求：预算为 30 万，当前教育背景为二本， 年级为大四， 专业为音乐表演，读研究生
将信息映射为字段:'budget','current_level_of_education', 'grade', 'major', 'application_stage'
</conversion_process>

<extracted_info>
{"budget":30,"current_level_of_education":"本科","grade":"大四","major":"音乐表演", "application_stage": "硕士"}
</extracted_info>
</Example>

<Example>
<chat_history>
你好
</chat_history>

<conversion_process>
There is no user information in the chat history.
</conversion_process>

<extracted_info>
{}
</extracted_info>
</Example>

<Example>
<chat_history>
顾问: 咱这边是家长还是同学？
客户: 同学
顾问: 咱们目前自己有什么想法？有没有特别想去的国家？
客户: 目前还没有
</chat_history>

<conversion_process>
识别关键需求：preferred_plan: 还没有。
将信息映射为字段: "preferred_plan"
</conversion_process>

<extracted_info>
{"preferred_plan": "还没有"}
</extracted_info>
</Example>

<Example> 
<chat_history>
客户：我孩子初一成绩不是很好，也就60分吧，后续有什么规划建议吗。暴叔 
暴叔：现在孩子还小，不着急出国。 
暴叔：你有多少预算？ 
客户：出国一般大概要准备多少，我没有什么概念。 
暴叔：70-80w还是要的。 
</chat_history>
<conversion_process> 
识别关键需求：初一，60分，当前询问身份是家长，预算和是否出国，暴叔都给了提议，但是家长没有给予肯定，所以不提取
将信息映射为字段:'current_level_of_education','grade','gpa', 'goal'
</conversion_process>

<extracted_info> 
{"current_level_of_education":"初中","grade":"初一","gpa": "60", "goal": "初中规划建议"} 
</extracted_info>
</Example>


<Example> 
<chat_history>
暴叔: 明白了，预算大概多少呢？
客户: 10w吧
暴叔: 10万预算有点紧张，考虑欧洲吗？
</chat_history>

<conversion_process> 
识别关键需求：预算为 10 万，暴叔建议考虑欧洲，但是不是客户的需求，不做意向国家的提取
将信息映射为字段:'budget'
</conversion_process>

<extracted_info> 
{"budget": 10} 
</extracted_info>
</Example>

Follow these steps to convert the input you receive into the appropriate JSON format.
Give your simple reasoning in <conversion_process> as examples.
Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.`)
  }, 60000)

  it('槽位合并', async () => {
    await MetaPrompt.getPrompt(`你需要根据我提供的槽位和槽位描述，对下面该槽位的数组进行合并,
对于语义重复的，只保留一个
对于语义接近的，可以进行合并
语义不同的，都进行保留     
     
比如：
"meditation_experience": "User's previous meditation experience, if any. Include both whether they have experience and what methods they've used if mentioned."

1. meditation_experience: ["有使用冥想应用的经验但未能保持持续练习"]
2. meditation_experience: ["曾尝试过冥想但没有坚持下来", "学习过禅修]

合并后的结果为：
{
  "meditation_experience": [...]
}`)
  }, 60000)

  it('predict user slots', async () => {
    let prompt = `You will be analyzing chat conversations to extract key information about a meditation course student's background, needs, and concerns. Your task is to extract specific fields from the conversation and format them as a JSON object.
    
Here is the chat conversation to analyze:

<chat_history>
{$CHAT_HISTORY}
</chat_history>

You should extract information related to the following fields:

{
    "meditation_experience": {
        "type": "string",
        "description": "User's previous meditation experience, if any. Include both whether they have experience and what methods they've used if mentioned."
    },
    "goals_and_needs": {
        "type": "string", 
        "description": "User's stated goals for meditation, for examples: focusing on stress relief, focus improvement, and/or sleep quality."
    },
    "pain_points": {
        "type": "string",
        "description": "User's main challenges and concerns, including sources of stress and doubts about meditation."
    },
    "practice_challenges": {
        "type": "string",
        "description": "Specific problems or difficulties encountered during meditation practice."
    }
}

Before extracting information, analyze the conversation in your scratchpad to identify relevant details. For example:

<example>
<chat_history>
客户：我之前用过冥想App，但是坚持不下来。最近工作压力很大，而且睡眠不好。我想学习正确的冥想技巧，但担心没有时间。
</chat_history>

<scratchpad>
识别关键信息：
- 冥想经验：曾使用冥想应用但未能坚持
- 目标：应对工作压力，改善睡眠
- 痛点：工作压力，时间限制
- 练习挑战：难以保持持续练习
</scratchpad>

<extracted_info>
{
    "meditation_experience": "有使用冥想应用的经验但未能保持持续练习",
    "goals_and_needs": "寻求缓解工作压力和改善睡眠质量",
    "pain_points": "经历工作压力且担心没有时间练习",
    "practice_challenges": "难以保持持续的冥想练习"
}
</extracted_info>
</example>

Key extraction rules:
1. Only extract information explicitly mentioned in the conversation
2. Use precise quotes or paraphrasing that closely matches the user's words
3. If a field has no relevant information in the conversation, set its value to null
4. Focus on the user's statements rather than the instructor's suggestions
5. If multiple relevant points exist for a field, combine them with commas

Begin your response by analyzing the conversation in your scratchpad, then present your structured extraction in JSON format within <extracted_info> tags.

Please proceed with the extraction.`

    prompt = prompt.replace('{$CHAT_HISTORY}', '老师，我婆媳关系不好，小孩学习也不好，我自己也有工作压力，学冥想想释放下压力，但是不知道怎么入门，有什么建议吗？')

    const res = await LLM.predict(prompt)

    console.log(res)
  }, 60000)


  it('判断财富果园画面', async () => {
    console.log(await MetaPrompt.getPrompt(`判断客户的描述是否为财富果园画面, 并返回 true or false
在课程中，老师会带练财富果园的冥想，课后告知客户记录下财富果园的景象，老师会帮助解读。景象包括果园里树木和大门，果子，围栏，四季的变换循环景色等。只有当客户明确提到需要财富果园解析或他们想象的财富果园里的景象时，才返回 true
例如: 
"没有大门,小冠木围栏，主树不清晰，没有看到结果,四季常青", "灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了
<result>
true
</result>

"没有画面"
<result>
true
</result>

"谢谢老师"
<result>
false
</result>

输出格式为：
<result>
true/false
</result>`))
  }, 60000)

  it('talk style prompt', async () => {
    console.log(await MetaPrompt.getPrompt(`你正在和客户聊天，你需要首先根据客户的信息和最近的聊天历史总结出客户的聊天风格，然后推测出客户更喜欢和拥有怎样聊天风格的人聊天。
请按以下格式输出：
<customer>
客户的聊天风格
</customer>
<style>
客户偏好的聊天风格
</style>`))
  }, 60000)

  it('distance prompt', async () => {
    console.log(await MetaPrompt.getPrompt(`你需要通过你和客户的聊天历史和客户的信息感知和推测客户和你的距离感和信任感，比如客户多次主动发起对话或主动暴露痛点等等对话信号。
请按以下格式输出：
<distance>
你感知到的客户对你的距离感和信任感，以及你的依据
</distance>`))
  }, 60000)
})