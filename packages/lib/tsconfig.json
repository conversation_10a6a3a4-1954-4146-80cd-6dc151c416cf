{
  "compilerOptions": {
    "allowJs": true,
    "strictPropertyInitialization": false,
    "sourceMap": true,
    "pretty": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "module": "commonjs",
    "target": "esnext",
    "strict": true,
    "moduleResolution": "node",
    "lib": [
      "ES2021",
      "es2020",
      "dom",
      "es5",
      "es6"
    ],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  },
  "exclude":["./jest.config.js","./silk"]
}
