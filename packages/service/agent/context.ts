import { BaseMessage, SystemMessage } from '@langchain/core/messages'
import { ChatHistoryService } from '../chat_history/chat_history'
import { IWorkflowState } from '../llm/state'

export interface AbstractContextBuilderOptions {
  state: IWorkflowState
  includeRAG?: boolean
  includeMemory?: boolean
  includeBehavior?: boolean
  includePortrait?: boolean
  includeTimeInfo?: boolean
  includeChatHistory?: boolean
  talkStrategyPrompt?: string
  customerChatRounds?: number
  injectChatHistory?: boolean
  customPrompt?: string
  debug?: boolean
}

export abstract class AbstractContextBuilder {
  protected options: AbstractContextBuilderOptions
  private chatHistoryServiceClient: ChatHistoryService

  protected constructor(options: AbstractContextBuilderOptions, chatHistoryServiceClient: ChatHistoryService) {
    // 设置默认参数
    this.options = {
      includeRAG: false,
      includeMemory: false,
      includeBehavior: false,
      includePortrait: true,
      includeTimeInfo: true,
      includeChatHistory: true,
      customerChatRounds: 6,
      injectChatHistory: false,
      debug: false,
      ...options
    }
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }

  private readonly TITLES = {
    AVATAR_SETTINGS: '# 角色设定',
    COURSE_CONFIG: '# 课程设置',
    COMPANY_PROFILE: '# 公司简介',
    RULE_LIMITS: '# 规则限制',
    RETRIEVED_KNOWLEDGE: '# 补充知识',
    CUSTOMER_MEMORY: '# 客户记忆',
    CUSTOMER_BEHAVIOR: '# 客户行为',
    CUSTOMER_PORTRAIT: '# 客户画像',
    DIALOG_HISTORY: '# 对话历史',
    TEMPORAL_INFORMATION: '# 时间信息',
    MAIN_TASK: '# 主要任务',
  } as const

  // 公共方法：格式化每段内容
  protected formatSection(content: string, title: string): string {
    return `${title}\n${content.trim()}`
  }

  // 主流程方法
  public async build(): Promise<BaseMessage[]> {
    const contexts: string[] = []

    if (this.options.customPrompt) { contexts.push(this.options.customPrompt) } else { contexts.push(
      await this.getAvatarSettings(), await this.getCompanyProfile(), await this.getRuleLimits(), await this.getCourseConfig(),) }

    if (this.options.includeRAG) { contexts.push(await this.getRetrievedKnowledge()) }

    if (this.options.includeMemory) { contexts.push(await this.getCustomerMemory()) }

    if (this.options.includeBehavior) { contexts.push(await this.getCustomerBehavior()) }

    if (this.options.includePortrait) { contexts.push(await this.getCustomerPortrait()) }

    if (this.options.injectChatHistory && this.options.customerChatRounds && this.options.customerChatRounds !== 0) { contexts.push(await this.getInjectedDialogHistory()) }

    if (this.options.includeTimeInfo) { contexts.push(await this.getTemporalInformation()) }

    if (this.options.talkStrategyPrompt) { contexts.push(this.formatSection(this.options.talkStrategyPrompt, this.TITLES.MAIN_TASK)) }

    const systemPrompt = contexts.map((s) => s.trim()).filter(Boolean).join('\n\n')
    const messages: BaseMessage[] = [new SystemMessage(systemPrompt)]

    // 附加对话历史（非注入）
    if (
      this.options.includeChatHistory &&
      this.options.customerChatRounds &&
      this.options.customerChatRounds !== 0 &&
      !this.options.injectChatHistory
    ) {
      const chatHistory = await this.getLLMDialogHistory(this.options.state.chat_id, this.options.customerChatRounds)
      messages.push(...chatHistory)
    }
    if (this.options.debug) { console.log(messages.map((m) => `${m.getType()}: ${m.content}`).join('\n\n')) }
    return messages
  }

  // 以下方法供子类覆写扩展
  protected async getAvatarSettings(): Promise<string> {
    return this.formatSection(await this.avatarSettings(), this.TITLES.AVATAR_SETTINGS)
  }
  protected async getCompanyProfile(): Promise<string> {
    return this.formatSection(await this.companyProfile(), this.TITLES.COMPANY_PROFILE)
  }
  protected async getRuleLimits(): Promise<string> {
    const content = `- 表现得像一个真人，说人话，避免承认自己是AI，当被怀疑是AI时巧妙化解，如“笑了，AI能这么智能...”
- 当客户的情绪比较激动或抽象时，可以使用😅表情来表示尴尬
- 聊天历史中以“[]”包含的单独一条内容为老师发送的营销信息
- 禁止提及任何废话类表达，如“随时找我”“太好了！”“太棒了！”或类似句式
- 始终通过文字私聊沟通，不涉及见面或电话交流
- 禁止提供任何手机号，微信号，物流单号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼`
    const extra = await this.extraRuleLimits()
    return this.formatSection(content + (extra ? `\n${extra}` : ''), this.TITLES.RULE_LIMITS)
  }
  protected async getCourseConfig(): Promise<string> {
    return this.formatSection(await this.courseConfig(this.options.state.chat_id), this.TITLES.COURSE_CONFIG)
  }
  protected async getRetrievedKnowledge(): Promise<string> {
    return this.formatSection(await this.retrievedKnowledge(this.options.state.userMessage, this.options.state.chat_id, this.options.state.round_id), this.TITLES.RETRIEVED_KNOWLEDGE)
  }
  protected async getCustomerMemory(): Promise<string> {
    return this.formatSection(await this.customerMemory(this.options.state.userMessage, this.options.state.chat_id), this.TITLES.CUSTOMER_MEMORY)
  }
  protected async getCustomerBehavior(): Promise<string> {
    return this.formatSection(await this.customerBehavior(this.options.state.chat_id), this.TITLES.CUSTOMER_BEHAVIOR)
  }
  protected async getCustomerPortrait(): Promise<string> {
    return this.formatSection(await this.customerPortrait(this.options.state.chat_id), this.TITLES.CUSTOMER_PORTRAIT)
  }
  protected async getInjectedDialogHistory(): Promise<string> {
    return (this.options.injectChatHistory && this.options.customerChatRounds && this.options.customerChatRounds !== 0) ?
      this.formatSection(await this.chatHistoryServiceClient.getChatHistory(this.options.state.chat_id, this.options.customerChatRounds, 18), this.TITLES.DIALOG_HISTORY) : ''
  }
  protected async getTemporalInformation(): Promise<string> {
    return this.formatSection(await this.temporalInformation(this.options.state.chat_id), this.TITLES.TEMPORAL_INFORMATION)
  }
  protected async getLLMDialogHistory(chatId: string, rounds: number): Promise<BaseMessage[]> {
    let dialogHistory: BaseMessage[]
    dialogHistory = await this.chatHistoryServiceClient.getLLMChatHistory(chatId, rounds)
    if (dialogHistory.length > 20) { dialogHistory = dialogHistory.slice(-20) }
    return dialogHistory
  }

  // 子类要实现的 getter 钩子
  protected abstract avatarSettings(): Promise<string>
  protected abstract companyProfile(): Promise<string>
  protected abstract extraRuleLimits(): Promise<string>
  protected abstract courseConfig(chatId: string): Promise<string>
  protected abstract retrievedKnowledge(userMessage: string, chatId: string, roundId: string): Promise<string>
  protected abstract customerMemory(userMessage: string, chatId: string): Promise<string>
  protected abstract customerBehavior(chatId: string): Promise<string>
  protected abstract customerPortrait(chatId: string): Promise<string>
  protected abstract temporalInformation(chatId: string): Promise<string>
}