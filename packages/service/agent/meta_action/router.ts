import logger from 'model/logger/logger'
import { IActionInfo, MetaActionComponent } from './component'

export interface MetaActionStage {
  thinkPrompt: string
  metaActions: string
  guidance: string
}

export class MetaActionRouter {
  public components: MetaActionComponent[]
  public actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {}

  constructor(componentList: MetaActionComponent[]) {
    this.components = componentList
  }

  public async initMetaAction(chat_id: string, round_id: string) {
    await this.registerActionList(chat_id, round_id)
  }

  private async registerActionList(chat_id: string, round_id: string) {
    for (const component of this.components) {
      const actionList = await component.getAction(chat_id, round_id)
      if (actionList) {
        this.actionMap = { ...this.actionMap, ...actionList }
      }
    }
  }

  public async handleAction(chat_id: string, round_id: string, actions: string[]): Promise<IActionInfo> {
    if (Object.keys(this.actionMap).length === 0) { await this.initMetaAction(chat_id, round_id) }
    const actionInfo: IActionInfo = { guidance: '' }
    if (!this.actionMap || Object.keys(this.actionMap).length == 0) { return actionInfo }
    for (const action of actions) {
      if (this.actionMap[action]) {
        return await this.actionMap[action](chat_id, round_id)
      }
    }
    return actionInfo
  }

  public async getThinkAndMetaActions(chat_id: string, round_id: string): Promise<MetaActionStage> {
    if (Object.keys(this.actionMap).length === 0) {
      await this.initMetaAction(chat_id, round_id)
    }

    let activeComponent: MetaActionComponent | null = null
    for (const component of this.components) {
      if (await component.isStageActive(chat_id)) {
        activeComponent = component
        break
      }
    }

    if (!activeComponent) {
      logger.error({ chat_id:chat_id }, '没有可用的元行为组')
      return {
        thinkPrompt: '',
        metaActions: '',
        guidance: '',
      }
    }

    logger.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeComponent.constructor.name}`)

    const thinkPrompt = await activeComponent.getThinkPrompt(chat_id)
    const metaAction = await activeComponent.getMetaAction(chat_id)

    return {
      thinkPrompt,
      metaActions: MetaActionRouter.formatMetaActionPrompt(metaAction),
      guidance: await activeComponent.getGuidance(chat_id),
    }
  }

  static formatMetaActionPrompt(metaAction:Record<string, string>): string {
    return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n')
  }
}