import { getPrompt } from '../prompt'
import * as hub from 'langchain/hub/node'
import { Runnable } from '@langchain/core/runnables'

// jest.mock('langchain/hub/node')

describe('getPrompt', () => {
  // 不用 mock，直接请求真实内容
  it('should fetch and print real prompt', async () => {
    const result = await getPrompt('free-think')
    console.log('Fetched real prompt:', result)
    expect(result).toBeDefined()
  })
})

describe('getPrompt', () => {
  const mockRunnable = {} as Runnable

  beforeEach(() => {
    // @ts-ignore
    hub.pull.mockClear()
    // @ts-ignore
    hub.pull.mockResolvedValue(mockRunnable)
  })

  it('should fetch and cache the prompt on first call', async () => {
    const result = await getPrompt('free-think')
    expect(hub.pull).toHaveBeenCalledWith('free-think')
    expect(result).toBe(mockRunnable)
  })

  it('should return cached prompt on subsequent calls', async () => {
    // 第一次调用拉取并缓存
    await getPrompt('planner')
    // 第二次调用应该不会再次调用 hub.pull
    await getPrompt('planner')
    expect(hub.pull).toHaveBeenCalledTimes(1)
  })
})