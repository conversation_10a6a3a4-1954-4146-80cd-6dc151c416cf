import logger from 'model/logger/logger'
import { BaseHumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { LLM } from 'lib/ai/llm/llm_model'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { XMLHelper } from 'lib/xml/xml'

export async function checkRobotDetection(
  chatStateStoreClient: ChatStateStore,
  humanTransferClient: BaseHumanTransfer,
  chat_id: string,
  round_id: string,
  user_id: string,
  userMessage: string
): Promise<boolean> {
  const robotDetectionNodeName = 'RobotDetected'
  const AI_KEYWORDS = ['人工', '机器人', '是真人', 'ai', '人机', '智能', '智慧体']
  const isRobotKeywordPresent = AI_KEYWORDS.some((keyword) => userMessage.toLowerCase().includes(keyword))

  if (!isRobotKeywordPresent) { return false }

  const promptTemplate = `# 检查AI识别
- 根据客户最近发言，判断其是否怀疑你是AI，机器人或智能体，若怀疑则输出 true，反之输出 false
- 先输出理由到 <think></think> 标签中，然后将结果 true 或者 false 输出到 <answer></answer> 标签中
- 如果客户提到的是AI软件，则不是怀疑你的身份是AI

## 输出示例
<think>客户说他之前做过AI，并没有怀疑你是AI</think><answer>false</answer>

## 客户输入
${userMessage}

开始输出`
  const robotDetectionPrompt = SystemMessagePromptTemplate.fromTemplate(promptTemplate, { templateFormat: 'mustache' })
  const res = await LLM.predict(robotDetectionPrompt, { meta: { promptName: 'robot_detection', chat_id: chat_id, round_id: round_id } }, {})
  const robotDetectionResult = XMLHelper.extractContent(res, 'answer')
  if (robotDetectionResult != 'true') { return false }

  await chatStateStoreClient.increaseNodeCount(chat_id, robotDetectionNodeName)
  const robotDetectedNum = await chatStateStoreClient.getNodeCount(chat_id, robotDetectionNodeName)
  const transferMessage = `${HumanTransferType.RobotDetected}\n客户：${userMessage}`

  if (robotDetectedNum >= 2) {
    try {
      await humanTransferClient.transfer(chat_id, user_id, transferMessage, true)
      await chatStateStoreClient.update(chat_id, { nodeInvokeCount: { [robotDetectionNodeName]: 0 } })
      return true
    } catch (error) {
      logger.error('客户识别到AI，但转人工失败:', error)
    }
  } else if (robotDetectedNum === 1) {
    await humanTransferClient.transfer(chat_id, user_id, transferMessage, 'onlyNotify')
  }
  return false
}