import { Chat, ChatDB } from '../database/chat'
import { JuziAPI } from 'model/juzi/api'
import { Config } from 'config'
import { ChatStateStore } from '../local_cache/chat_state_store'
import logger from 'model/logger/logger'
import { GroupNotification } from '../group_notification/group_notification'

export enum HumanTransferType {
    RobotDetected = '客户识别到了AI',
    UnknownMessageType = 1,
    NotBindPhone = 2,
    ProblemSolving = 3,
    FailedToJoinGroup = 4,
    ProcessImage = 5,
    MessageSendFailed = 6,
    ReaskAnotherDay = 7,
    HesitatePayment = 8,
    LogOutNotify = 9,
    ExecutePostpone = 10,
    VoiceOrVideoCall = 11,
    SoftwareIssue = 12,
    PaidCourse = 14,
    ProcessVideo = 24,
    ProcessVideoFailed = 25,
    ExplicitlyPurchases = 26,
    ExecuteRefresherTraining = 27
}

export class BaseHumanTransfer {
  chatDBClient:ChatDB
  chatStateStoreClient:ChatStateStore

  constructor(chatDBClient:ChatDB, chatStateStoreClient:ChatStateStore) {
    this.chatDBClient = chatDBClient
    this.chatStateStoreClient = chatStateStoreClient
  }

  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param notifyMessage
   * @param toHuman
   * @param imBotId
   */

  public async transfer(chatId: string, userId: string, notifyMessage: string, toHuman: boolean |'onlyNotify' = true, imBotId?: string) {
    const chat = await this.chatDBClient.getById(chatId) as Chat
    if (typeof toHuman === 'boolean' && chat) {
      await this.chatDBClient.setHumanInvolvement(chatId, toHuman)
    } else {
      if (!chat) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await this.chatDBClient.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: await this.chatStateStoreClient.get(chatId)
        })
      }
    }

    // 包装通知
    let contactName = userId
    let notificationMessage: string
    const isPaid = chat.chat_state.state.is_complete_payment ?? false
    if (chat && chat.contact && chat.contact.wx_name) {
      contactName = chat.contact.wx_name
    }
    notificationMessage = `${contactName} ${isPaid ? '💰' : ''}${notifyMessage}`

    if (toHuman === true) { notificationMessage += '\n[心碎]AI已关闭[心碎]' }
    logger.log(chatId, '通知人工接入：', notificationMessage)
    if (toHuman === 'onlyNotify' && Config.setting.localTest) { return }

    await GroupNotification.notify(notificationMessage,  imBotId) // 群通知
  }
}