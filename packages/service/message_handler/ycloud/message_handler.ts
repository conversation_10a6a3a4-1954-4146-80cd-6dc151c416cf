import { MessageReplyService } from '../reply/message_reply'
import { Que<PERSON>, Worker } from 'bullmq'
import { LRUCache } from 'lru-cache'
import { Config } from 'config'
import { getChatId } from 'config/chat_id'
import { RedisCacheDB } from 'model/redis/redis_cache'
import logger from 'model/logger/logger'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { sleep } from 'lib/schedule/schedule'
import { DateHelper } from 'lib/date/date'
import XunfeiASR from 'model/nls/xunfei'
import { ChatDB } from '../../database/chat'
import { ChatInterruptHandler } from '../interrupt/interrupt_handler'
import { ChatStateStore } from '../../local_cache/chat_state_store'
import { BaseWorkFlow } from '../../workflow/workflow'
import { WhatsappInboundMessage, WhatsappInboundMessageType } from '@ycloud-cpaas/ycloud-sdk-node'
export { WhatsappInboundMessage }


interface YCloudMessageHandlers {
  handleUnknownMessage(message: WhatsappInboundMessage): Promise<any>
  handleImageMessage(imageUrl: string, chatId: string): Promise<string> // 返回图片转文字结果
  handleVideoMessage(videoUrl: string, chatId: string): Promise<string> // 返回视频转文字结果
  sendWelcomeMessage(chatId: string, phoneNumber: string, name:string): Promise<void>
  workflow:typeof BaseWorkFlow
}

/**
 * 用于管理全局消息队列，为每个客户设置单独的计时器，合并处理消息
 */
export class YCloudMessageHandler {
  private readonly handlers: YCloudMessageHandlers
  private readonly messageSet = new LRUCache<string, any>({ max: 3000 })
  private _messageQueueBullMQ: Queue
  private messageQueueWorker?: Worker
  private chatDBClient:ChatDB
  private chatHistoryServiceClient:ChatHistoryService
  private chatStateStoreClient:ChatStateStore
  private messageReplyServiceClient:MessageReplyService

  constructor(handlers: YCloudMessageHandlers, chatDBClient:ChatDB, chatHistoryServiceClient:ChatHistoryService, chatStateStoreClient:ChatStateStore, messageReplyServiceClient:MessageReplyService) {
    this.handlers = handlers
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.chatStateStoreClient = chatStateStoreClient
    this.messageReplyServiceClient = messageReplyServiceClient
  }

  private get messageQueueBullMQ():Queue {
    if (!this._messageQueueBullMQ) {
      this._messageQueueBullMQ = new Queue(this.queueName, {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this._messageQueueBullMQ
  }

  private getMessageStoreName(userId: string) {
    const chatId = getChatId(userId)

    return `ycloud-user-message-store_${chatId}`
  }

  private get queueName() {
    return `ycloud-user-message-queue_${Config.setting.wechatConfig!.id}`
  }

  public async handle (message: WhatsappInboundMessage) {
    if (!message.id || !message.from || !message.to) {
      return
    }
    const unsupportedType:WhatsappInboundMessageType[] = [WhatsappInboundMessageType.Button, WhatsappInboundMessageType.Contacts, WhatsappInboundMessageType.Document, WhatsappInboundMessageType.Interactive, WhatsappInboundMessageType.Location, WhatsappInboundMessageType.Order, WhatsappInboundMessageType.Reaction, WhatsappInboundMessageType.RequestWelcome, WhatsappInboundMessageType.System, WhatsappInboundMessageType.Unsupported]

    if (unsupportedType.includes(message.type ?? WhatsappInboundMessageType.Unsupported)) {
      return
    }

    if (message.id && this.messageSet.has(message.id)) {
      return
    }

    if (message.type == 'text') {
      logger.debug(`接收消息 from:${message.from} to:${message.to} text:${message.text?.body}`)
      // 文本消息，提前 increment 一下 version 来即时打断
      await ChatInterruptHandler.incrementChatVersion(getChatId(message.from))
    }

    this.messageSet.set(message.id, 1)

    const senderId = message.from

    // 如果是账号挂上之前的老客户不进行回复
    if (await this.isPastUser(message.from)) {
      return
    }


    try {
      const messageStore = new RedisCacheDB(this.getMessageStoreName(senderId)) // 改为 message_store + chatId 来存储，否则可能有 key 冲突问题
      // 将消息存到 Redis
      await messageStore.addSet(JSON.stringify(message))

      let delay =  Config.setting.waitingTime.messageMerge
      if (Config.isTestAccount()) {
        delay = 5 * 1000
      }

      // 添加消息，到消息队列
      await this.messageQueueBullMQ.add(senderId, { userId: senderId, messageId:message.id }, { delay, removeOnComplete: true, removeOnFail: true })
    } catch (e) {
      logger.error('消息添加到任务队列失败', e)
    }
  }

  public startWorker() {
    if (!this.messageQueueWorker) {
      this.messageQueueWorker = new Worker(this.queueName, async (job) => {
        const { userId, messageId } = job.data

        // 只处理最新的消息
        const isLatest = await this.isLatestMessage(userId, messageId)
        if (!isLatest) {
          logger.debug(`跳过非最新消息: ${messageId}`)
          return
        }

        // 如果最后一条消息 是 AI 消息，并跟现在的时间比较接近，则做下延迟处理，拖延一下回复速度
        const messages =  await this.chatHistoryServiceClient.getChatHistoryByChatId(getChatId(userId))
        if (messages.length > 0) {
          const lastAIMessageReplyDiff = DateHelper.diff(messages[messages.length - 1].created_at, new Date(), 'second')
          if (lastAIMessageReplyDiff < 5) {
            await sleep(5 * 1000)
          }
        }

        await this.processUserMessages(userId)
      }, { connection: RedisCacheDB.getInstance(), concurrency: 20 })
    }
  }

  private async isLatestMessage(userId: string, messageId: string) {
    const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
    const messages:WhatsappInboundMessage[] = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      return false
    }

    // 消息有可能不是按顺序接收的，需要按时间重排序下
    messages.sort((msgA:WhatsappInboundMessage, msgB:WhatsappInboundMessage) => {
      if (msgA.sendTime && msgB.sendTime) {
        return msgA.sendTime.localeCompare(msgB.sendTime)
      } else {
        return -1
      }
    })

    return messages[messages.length - 1].id === messageId
  }

  public async processUserMessages (userId: string) {
    try {
      // 获取该客户在这段时间内发送的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
      const userMessages:WhatsappInboundMessage[] = await messageStore.getSetMembers()

      // 消息有可能不是按顺序接收的，需要按时间重排序下
      userMessages.sort((msgA:WhatsappInboundMessage, msgB:WhatsappInboundMessage) => {
        if (msgA.sendTime && msgB.sendTime) {
          return msgA.sendTime?.localeCompare(msgB.sendTime)
        } else {
          return -1
        }
      })

      // 从消息队列中移除已处理的消息
      await messageStore.del()

      // 将消息转为文本
      const texts = await this.getTextsFromMessages (userMessages, userId)

      await this.messageReplyServiceClient.reply(texts, userId)
    } catch (e) {
      console.error (`处理客户 ${userId} 的消息出错：`, e)
    }
  }

  private async getTextsFromMessages(messages: WhatsappInboundMessage[], userId: string): Promise<string[]> {
    const texts : string[] = []
    const ignoreMessage = false // 是否忽略当前消息
    for (const message of messages) {
      try {
        if (message.id) {
          const cache = new RedisCacheDB(message.id)
          await cache.set(message, 3 * 60 * 60) // 缓存 3小时
        }
      } catch (e) {
        logger.warn('缓存消息失败')
      }


      try {
        let text = ''

        // 忽略欢迎语
        if (message.type === 'text' && await this.isFirstMessage(userId)) {
          const chatId = getChatId(userId)
          const flags = await this.chatStateStoreClient.getFlags<any>(chatId)
          if (flags.is_friend_accepted) {
            continue
          }

          // 如果没有 欢迎语的话，进入欢迎语流程
          await this.handlers.sendWelcomeMessage(getChatId(userId), message.from ?? '', message.customerProfile?.name ?? '')
          continue
        }

        switch (message.type) {
          case WhatsappInboundMessageType.Text: {
            text = message.text?.body ?? ''
            break
          }

          case WhatsappInboundMessageType.Sticker:
            text = '[表情]'
            break

          case WhatsappInboundMessageType.Audio: {
            const xunfei = new XunfeiASR({
              appId: Config.setting.xunfeiASR.appId,
              secretKey: Config.setting.xunfeiASR.secretKey,
              uploadFileUrl: message.audio?.link
            })

            text = await xunfei.getResult()

            break }

          case WhatsappInboundMessageType.Image: {
            text = await this.handleImageMessage(getChatId(userId), message) ?? text
            break }

          case WhatsappInboundMessageType.Video: {
            text = await this.handleVideoMessage(getChatId(userId), message) ?? text
            break
          }
          default:
            logger.log(JSON.stringify(message, null, 4))
            await this.handleUnknownMessageType(message)
        }
        if (message.context?.id) {
          const citationMessage = await this.chatHistoryServiceClient.getMessageByMessageId(message.context.id)
          if (citationMessage?.short_description) {
            text = `对[${citationMessage.short_description}]的回复是：\n${text}`
          } else if (citationMessage?.content) {
            text = `对[${citationMessage.content}]的回复是：\n${text}`
          }
        }
        texts.push(text)

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        logger.error(`单条消息解析出错：${JSON.stringify(e)}`,)
      }
    }

    if (ignoreMessage) {
      return []
    }

    return texts
  }

  public async handleUnknownMessageType(message: WhatsappInboundMessage) {
    if (!message.from)
      return

    const chat_id = getChatId(message.from)
    logger.log(chat_id, '非文本消息类型', message.type)

    if (await this.chatDBClient.isHumanInvolvement(chat_id)) { // 已经人工参与了，不再处理
      return
    }

    await this.handlers.handleUnknownMessage(message)
  }


  private async isFirstMessage(userId: string) {
    const chatId = getChatId(userId)

    return await this.chatHistoryServiceClient.getMessageCount(chatId) === 0
  }

  private async handleImageMessage(chatId: string, message: WhatsappInboundMessage) {

    if (!message.image || !message.image?.link) {
      logger.warn(`这条消息没有imageUrl,${JSON.stringify(message)}`)
      return ''
    }
    const originalImageUrl: string = message.image.link
    return this.handlers.handleImageMessage(originalImageUrl, chatId)
  }

  private async handleVideoMessage(chatId: string, message: WhatsappInboundMessage) {

    // message中的payload有视频url
    const videoUrl = message.video?.link
    if (!videoUrl) {
      logger.warn(`这条消息没有videoUrl,${JSON.stringify(message)}`)
      return ''
    }
    logger.debug(`>>> DEBUG: videoUrl = ${videoUrl}`)
    return this.handlers.handleVideoMessage(videoUrl, chatId)
  }

  public async isPastUser(senderId: string) {
    try {
      const accountCreatedTime = Config.setting.wechatConfig?.createdTime // 挂号时间

      if (!accountCreatedTime) {
        return false
      }

      if (senderId === Config.setting.wechatConfig?.id) {
        return false
      }

      return false
    } catch (e) {
      return false
    }
  }
}