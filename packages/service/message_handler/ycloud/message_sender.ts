import { Config } from 'config'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { Event, WhatsappMessagesApi, Configuration, WhatsappMessageContact, WhatsappMessageContext, WhatsappMessageInteractive, WhatsappMessageLocation, WhatsappMessageMedia, WhatsappMessageReaction, WhatsappMessageTemplate, WhatsappMessageText, WhatsappMessageType } from '@ycloud-cpaas/ycloud-sdk-node'

import logger from 'model/logger/logger'
import { getBotId, getUserId } from 'config/chat_id'
import * as ycloud from '@ycloud-cpaas/ycloud-sdk-node'
import { UUID } from 'lib/uuid/uuid'

export type { Event, WhatsappMessageContact, WhatsappMessageContext, WhatsappMessageInteractive, WhatsappMessageLocation, WhatsappMessageMedia, WhatsappMessageTemplate, WhatsappMessageText, WhatsappMessageType }
export { WhatsappMessagesApi, Configuration }

export type WhatsappCanSentMessageType = WhatsappMessageTemplate|WhatsappMessageText|WhatsappMessageMedia|WhatsappMessageLocation|WhatsappMessageInteractive|Array<WhatsappMessageContact>|WhatsappMessageMedia|WhatsappMessageContext

interface YCloudSendMsg {
    chat_id: string
    ai_msg: string // 放到聊天记录中的文本信息，在发送纯文本情况下与 send_msg 相同。 当发送文件或资料时，此处有可能是占位符，可以单独设置 send_msg 为 [xx文件.txt] 之类。
    type: WhatsappMessageType
    send_msg?: WhatsappCanSentMessageType
}

interface YCloudSendOptions {
  shortDes?: string // 简短描述
  round_id?: string // 轮次 id, 用于记录模型输出
  sop_id?: string
}

export class YCloudMessageSender {
  client:ycloud.WhatsappMessagesApi
  chatHistoryServiceClient:ChatHistoryService
  constructor(client:ycloud.WhatsappMessagesApi, chatHistoryServiceClient:ChatHistoryService) {
    this.client = client
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }
  public async sendById(msg: YCloudSendMsg, options?: YCloudSendOptions) {
    if (msg.chat_id.split('_').length != 2) {
      logger.warn(`发送YCloud消息的时候chat_id格式不对,chatId:${msg.chat_id}`)
      return
    }
    const userId = getUserId(msg.chat_id)
    const botId = getBotId(msg.chat_id)

    let messageId:string | undefined = UUID.short()

    if (Config.setting.localTest) {
      // add Bot Message 中会 Print 消息
    } else {
      if (!msg.send_msg) {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type: 'text',
          text:{
            body: msg.ai_msg
          },
        })
        messageId = result.data.wamid
      } else if (msg.type == 'template') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'template',
          template:msg.send_msg as WhatsappMessageTemplate
        })
        messageId = result.data.wamid
      } else if (msg.type == 'text') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'text',
          text:msg.send_msg as WhatsappMessageText
        })
        console.log(result.data)
        messageId = result.data.wamid
      } else if (msg.type == 'image') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'image',
          image:msg.send_msg as WhatsappMessageMedia
        })
        messageId = result.data.wamid
      } else if (msg.type == 'video') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'video',
          video:msg.send_msg as WhatsappMessageMedia
        })
        messageId = result.data.wamid
      } else if (msg.type == 'audio') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'audio',
          audio:msg.send_msg as WhatsappMessageMedia
        })
        messageId = result.data.wamid
      } else if (msg.type == 'document') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'document',
          document:msg.send_msg as WhatsappMessageMedia
        })
        messageId = result.data.wamid
      } else if (msg.type == 'sticker') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'sticker',
          sticker:msg.send_msg as WhatsappMessageMedia
        })
        messageId = result.data.wamid
      } else if (msg.type == 'location') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'location',
          location:msg.send_msg as WhatsappMessageLocation
        })
        messageId = result.data.wamid
      } else if (msg.type == 'interactive') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'interactive',
          interactive:msg.send_msg as WhatsappMessageInteractive
        })
        messageId = result.data.wamid
      } else if (msg.type == 'contacts') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'contacts',
          contacts:msg.send_msg as Array<WhatsappMessageContact>
        })
        messageId = result.data.wamid
      } else if (msg.type == 'reaction') {
        const result = await this.client.sendDirectly({
          from: botId,
          to: userId,
          type:'reaction',
          reaction:msg.send_msg as WhatsappMessageReaction
        })
        messageId = result.data.wamid
      } else {
        logger.warn(`未知的类型: ${msg.type}`)
      }
    }

    if (options?.shortDes && !options?.shortDes.startsWith('[')) {
      options.shortDes = `[${options.shortDes}]`
    }

    await this.chatHistoryServiceClient.addBotMessage(msg.chat_id, msg.ai_msg, options?.shortDes, { message_id:messageId, round_id: options?.round_id, sop_id:options?.sop_id, state:'sent' })
  }
}
