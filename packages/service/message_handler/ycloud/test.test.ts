import { Configuration, WhatsappMessagesApi, WhatsappMessageTemplateComponent, WhatsappTemplatesApi } from '@ycloud-cpaas/ycloud-sdk-node'

describe('test', () => {
  jest.setTimeout(600000)
  const ycloudApiKey = 'b129ab33adb30bce3e9c0e77defd8011'
  const configuration = new Configuration({ apiKey: ycloudApiKey })
  it('test', async() => {
    const ycloud = new WhatsappTemplatesApi(configuration)
    const list = await ycloud.list({ filterName:'f_v1e_4_1626_notice1' })
    console.dir(list.data, { depth:Infinity })
  })

  it('send', async() => {
    const messageClient = new WhatsappMessagesApi(configuration)
    try {
      await messageClient.sendDirectly({
        from: '+85246439149',
        to: '+8618279289527',
        type: 'template',
        template:{
          name: 'f_v1e_4_1626_notice1',
          language: {
            code:'en'
          },
          components:[
          <WhatsappMessageTemplateComponent>{
            type:'body',
            parameters:[{
              type:'text',
              text:'hello_world'
            }]
          },
          <WhatsappMessageTemplateComponent>{
            type:'header',
            parameters:[{
              type:'image',
              image:{
                link:'https://oss-ycloud-publicread.oss-ap-southeast-1.aliyuncs.com/online/BASE-FILE/2025/07/30/398c1395-65a9-4eb8-ab6c-8d98e5ff7b13.jpg'
              }
            }]
          },
          ]
        }
      })
    } catch (e) {
      console.dir(e, { depth:Infinity })
    }
  })
})