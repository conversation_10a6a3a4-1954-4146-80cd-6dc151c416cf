import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import logger from 'model/logger/logger'
import Siliconflow from 'lib/sliconflow/siliconflow'
import ElasticSearchService, { ElasticSearchClient } from 'model/elastic_search/elastic_search'
import { AzureOpenAIEmbedding } from 'lib/ai/llm/openai_embedding'
import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import { Document } from 'langchain/document'
import { Config } from 'config'
import { QueryRewritePrompt } from './prompt/query_rewrite'
import { ChatHistoryService } from '../chat_history/chat_history'

interface RerankResultQuestions {
  q: string
  a: string
  score: number
}

interface QAs {
  q: string
  a: string
}

interface IRerankOptions {
  model?: 'BAAI/bge-reranker-v2-m3' | 'netease-youdao/bce-reranker-base_v1'
  top_n: number // 返回的结果数量
  return_documents?: boolean // 是否返回document里的内容
  max_chunks_per_doc?: number // 每个文档最多返回的chunk数量
  overlap_tokens?: number // 重叠的token数量
  threshold?: number // 小于 threshold 的 文档会被过滤掉
}

interface IQA {
  q: string
  a: string
}


/**
 * RAG Service，抽象出一些通用方法
 */
export class RAGHelper {
  chatHistoryServiceClient:ChatHistoryService

  constructor(chatHistoryServiceClient:ChatHistoryService) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
  }
  /**
   * 创建 RAG 库
   * @param index_name 索引名
   * @param metadata 要包含的字段，会使用 keyword 类型
   * @param queryName 查询的 query 字段名, 会自动启用分词，用于后续的混合检索
   */
  public static async createRAG(
    index_name: string,
    metadata: string[],
    queryName: string
  ): Promise<void> {
    // 构建基础的索引配置
    const indexConfig: Record<string, any> = {
      properties: {
        embeddings: { type: 'dense_vector', index: true }, // 嵌入向量字段
      },
    }

    // 将 metadata 中的字段添加到索引配置，使用 keyword 类型
    metadata.forEach((field) => {
      indexConfig.properties[`metadata.${field}`] = { type: 'keyword' }
    })

    // 为 queryName 字段创建分词器
    indexConfig.properties[queryName] = {
      type: 'text',
      analyzer: 'ik_max_word',
      search_analyzer: 'ik_smart',
    }

    // 创建索引
    await ElasticSearchService.createIndex(index_name, indexConfig)
  }

  /**
   * 插入数据，使用 LangChain 来辅助插入
   */
  public static async addDocuments(
    index_name: string,
    docs: Document[]
  ): Promise<void> {
    const clientArgs: ElasticClientArgs = {
      client: ElasticSearchClient.getInstance(),
      indexName: index_name,
    }

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)

    const batchSize = 100
    const batches = Math.ceil(docs.length / batchSize)
    for (let i = 0; i < batches; i++) {
      const batch = docs.slice(i * batchSize, (i + 1) * batchSize)

      await vectorStore.addDocuments(batch)
      logger.trace(`Insert ${batch.length} documents`)
    }
  }
  public static async addDocumentsWithIds(
    index_name: string,
    docs: Document[],
    ids: string[]
  ): Promise<void> {
    const clientArgs: ElasticClientArgs = {
      client: ElasticSearchClient.getInstance(),
      indexName: index_name,
    }

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)

    const batchSize = 100
    const batches = Math.ceil(docs.length / batchSize)
    for (let i = 0; i < batches; i++) {
      const batch = docs.slice(i * batchSize, (i + 1) * batchSize)
      const batchIds = ids.slice(i * batchSize, (i + 1) * batchSize)

      await vectorStore.addDocuments(batch, { ids: batchIds })
      logger.trace(`Insert ${batch.length} documents`)
    }
  }

  /**
   * 返回 改写的 subQuery + 原始 query
   * @param query
   * @param chat_id
   * @param round_id
   */
  public async queryReWriteWithChatHistory(
    query: string,
    chat_id: string,
    round_id: string
  ): Promise<string[]> {
    try {
      const chatHistory = await this.chatHistoryServiceClient.getChatHistory(chat_id, 3, 6)

      const prompt = await QueryRewritePrompt.format(query, chatHistory)
      const llm = new LLM({
        model: 'gpt-4.1-mini',
        meta: {
          promptName: QueryRewritePrompt.name,
          chat_id: chat_id,
          round_id: round_id,
        },
      }) // 添加日志，方便追踪
      const extractedRawQuery = await llm.predict(prompt)

      const subQueries = XMLHelper.extractContents(extractedRawQuery, 'query')
      if (!subQueries) {
        return [query]
      }

      return [query, ...subQueries]
    } catch (e) {
      logger.error('Error in query rewrite:', e)
      return [query]
    }
  }

  /**
   *
   * 将 Embedding 检索后的内容进行重排序
   * @param originQuery 重写的客户问题(string)
   * @param retrievedQAs embedding search得到的k个问题
   * @param options
   * @private
   */
  public static async reRank(
    originQuery: string,
    retrievedQAs: IQA[],
    options?: IRerankOptions
  ): Promise<RerankResultQuestions[]> {
    if (!retrievedQAs.length) {
      return []
    }

    const siliconFlow = new Siliconflow()

    siliconFlow.auth(Config.setting.siliconFlow.apiKey)

    try {
      const response = await siliconFlow.createRerank({
        model: options?.model ? options?.model : 'BAAI/bge-reranker-v2-m3',
        query: originQuery,
        documents: retrievedQAs.map((qa) => qa.q),
        top_n: options?.top_n,
        ...options,
      })

      // 根据相关性得分对结果进行排序
      const sortedResults = response.results.sort(
        (a, b) => b.relevance_score - a.relevance_score
      )

      // 返回排序后的文档
      return sortedResults.map((sortedResult) => {
        return {
          q: retrievedQAs[sortedResult.index].q,
          a: retrievedQAs[sortedResult.index].a,
          score: sortedResult.relevance_score,
        }
      })
    } catch (error) {
      console.error('Error in reRanker:', error)

      return retrievedQAs.map((qa) => {
        return {
          q: qa.q,
          a: qa.a,
          score: 0,
        }
      })
    }
  }

  static removeDuplicateQA(reRankResults: QAs[]) {
    const set = new Set<string>()

    return reRankResults.filter((qa) => {
      const combined = qa.q + qa.a
      if (!set.has(combined)) {
        set.add(combined)
        return true
      }

      return false
    })
  }

  static isNeedRAG(query: string) {
    if (!query || (query.startsWith('[') && query.endsWith(']'))) {
      // 输入过滤
      return false
    }

    // 检查是否需要跳过RAG查询
    const shouldSkipRag =
      /\d{11,}/.test(query) ||
      /^[a-zA-Z]+$/.test(query) ||
      query.trim().length <= 2

    if (shouldSkipRag) {
      return false
    }

    return true
  }
}