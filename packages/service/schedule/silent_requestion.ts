import { Queue, Worker, Job } from 'bullmq'
import { ChatInterruptHandler } from '../message_handler/interrupt/interrupt_handler'
import { RedisCacheDB } from 'model/redis/redis_cache'
import logger from 'model/logger/logger'
import { UUID } from 'lib/uuid/uuid'
import { Config } from 'config'

interface IReAskOptions {
  auto_retry?: boolean // 被新发送的客户消息打断后，是否进行自动重试，true 的话，直到没有新消息，后续会再次重试执行。默认为 false, 被打断后不再执行
  independent?: boolean // 如果为 true, 不将之前的任务取消。默认取消之前的任务
}

interface ISilentReAskTaskData {
  chat_id: string
  task_id: string
  task_name: string // 预注册的任务名称
  task_params?: any // 任务参数，可序列化的数据
  waiting_time: number
  options?: IReAskOptions
  initial_message_hash: number // 初始消息哈希，用于检查是否有新消息
  retry_count?: number // 重试次数
}

// 任务函数类型定义
type TaskFunction = (chat_id: string, params?: any) => Promise<void>

/**
 * 追问任务
 * 用于在客户没有回复的情况下，自动进行追问
 * 注意：
 * 1. 每个对话都有独立的追问计时
 * 2. 每次添加新的追问，都要清除之前的追问任务，同时只能有一个追问任务在执行
 * 3. 任务函数需要预先注册，支持服务重启后恢复
 */
export class SilentReAsk {
  private static queue: Queue | null = null
  private static worker: Worker | null = null

  // 预注册的任务函数映射
  private static REGISTERED_TASKS: Map<string, TaskFunction> = new Map()

  private static getQueueName(): string {
    return `silent_reask_queue_${Config.setting.wechatConfig?.id}`
  }

  /**
   * 获取队列实例
   */
  public static getQueue(): Queue {
    if (!this.queue) {
      this.queue = new Queue(this.getQueueName(), {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this.queue
  }

  /**
   * 启动工作进程
   */
  public static startWorker(): void {
    if (this.worker) {
      return
    }

    this.worker = new Worker(this.getQueueName(), async (job: Job<ISilentReAskTaskData>) => {
      await this.processTask(job)
    }, {
      connection: RedisCacheDB.getInstance(),
      concurrency: 10
    })

    this.worker.on('error', (error) => {
      logger.error('SilentReAsk worker error:', error)
    })
  }

  /**
   * 注册任务函数
   * @param taskName 任务名称
   * @param taskFunction 任务函数
   */
  public static registerTask(taskName: string, taskFunction: TaskFunction): void {
    if (this.REGISTERED_TASKS.has(taskName)) {
      logger.warn(`Task '${taskName}' already registered`)
      return
    }

    this.REGISTERED_TASKS.set(taskName, taskFunction)
  }

  /**
   * 处理任务
   */
  private static async processTask(job: Job<ISilentReAskTaskData>): Promise<void> {
    const { chat_id, task_name, task_params, options, initial_message_hash, retry_count = 0 } = job.data

    try {
      // 检查是否有新消息
      const currentMessageHash = await ChatInterruptHandler.getChatVersion(chat_id)
      const hasNewMessage = currentMessageHash !== initial_message_hash

      if (hasNewMessage) {
        // 有新消息，检查是否需要重试
        if (options?.auto_retry) {
          logger.debug(`Chat ${chat_id} has new messages, scheduling retry ${task_name}`)

          // 重新调度任务
          const newTaskData: ISilentReAskTaskData = {
            ...job.data,
            initial_message_hash: currentMessageHash,
            retry_count: retry_count + 1
          }

          await this.getQueue().add(
            `${task_name}_retry_${retry_count + 1}_${chat_id}`,
            newTaskData,
            { delay: 5 * 60 * 1000 }
          )

          return
        }
      }

      // 没有新消息，执行任务
      const taskFunction = this.REGISTERED_TASKS.get(task_name)
      if (taskFunction) {
        await taskFunction(chat_id, task_params)
        logger.debug(`Silent reask task '${task_name}' executed for chat ${chat_id}`)
      } else {
        logger.error(`Task '${task_name}' not found in registered tasks. Available tasks: ${Array.from(this.REGISTERED_TASKS.keys()).join(', ')}`)
      }
    } catch (error) {
      logger.error(`Error processing silent reask task '${task_name}' for chat ${chat_id}:`, error)
    }
  }

  /**
   * 添加追问任务
   * @param task_name 预注册的任务名称
   * @param chat_id 聊天ID
   * @param waiting_time 等待时间（毫秒）
   * @param task_params 任务参数（可选，必须可序列化）
   * @param options 选项, auto_retry 默认开启
   */
  public static async schedule(
    task_name: string,
    chat_id: string,
    waiting_time: number,
    task_params?: any,
    options?: IReAskOptions
  ): Promise<void> {
    // 检查任务是否已注册
    if (!this.REGISTERED_TASKS.has(task_name)) {
      logger.error(`Task '${task_name}' is not registered. Please register it first using SilentReAsk.registerTask(). Available tasks: ${Array.from(this.REGISTERED_TASKS.keys()).join(', ')}`)
      return
    }

    // 获取当前消息哈希
    const initial_message_hash = await ChatInterruptHandler.getChatVersion(chat_id)

    // 如果不是独立任务，取消之前的任务
    if (!options?.independent) {
      await this.cancelPreviousTasks(chat_id)
    }

    if (options?.auto_retry === undefined) { // 默认为 true
      options = {
        ...options,
        auto_retry: true
      }
    }

    // 创建任务数据
    const taskData: ISilentReAskTaskData = {
      chat_id,
      task_id: UUID.v4(),
      task_name,
      task_params,
      waiting_time,
      options,
      initial_message_hash,
      retry_count: 0
    }

    // 生成唯一的作业名称
    const jobName = `${task_name}_${chat_id}_${Date.now()}`

    // 添加到队列
    await this.getQueue().add(jobName, taskData, {
      delay: waiting_time,
      removeOnComplete: 10, // 保留最近10个完成的任务
      removeOnFail: 10      // 保留最近10个失败的任务
    })

    logger.debug(`Silent reask task '${task_name}' scheduled for chat ${chat_id}, delay: ${waiting_time}ms`)
  }

  /**
   * 取消指定 chat_id 的之前任务
   */
  private static async cancelPreviousTasks(chat_id: string): Promise<void> {
    try {
      const queue = this.getQueue()

      // 获取等待中的任务
      const waitingJobs = await queue.getJobs(['waiting', 'delayed'])

      // 找到相同 chat_id 的任务并取消
      for (const job of waitingJobs) {
        const data = job.data as ISilentReAskTaskData

        if (data && data.chat_id === chat_id && data.options?.independent !== true) {
          await job.remove()
          logger.debug(`Cancelled previous task '${job.data.task_name}' for chat ${chat_id}`)
        }
      }
    } catch (error) {
      logger.error(`Error cancelling previous tasks for chat ${chat_id}:`, error)
    }
  }
}