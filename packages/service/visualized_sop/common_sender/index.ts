import { commonSleep } from 'lib/schedule/schedule'
import { <PERSON><PERSON><PERSON>, MessageAudio, MessageFile, MessageImage, MessageSendOption, MessageSticker, MessageText, MessageVideo, MessageWecomCard, MessageWecomVideoChannel, MessageWecomVoice, MessageYCloudTemplate, SendMessageType } from './type'

export abstract class MessageSender {
  hasRepeatedMsg:HasRepeatedMsg
  constructor(hasRepeatedMsg:HasRepeatedMsg) {
    this.hasRepeatedMsg = hasRepeatedMsg
  }
  abstract sendText(chatId:string, msg:MessageText, opt?:MessageSendOption):Promise<void>
  abstract sendImage(chatId:string, msg:MessageImage, opt?:MessageSendOption):Promise<void>
  abstract sendVideo(chatId:string, msg:MessageVideo, opt?:MessageSendOption):Promise<void>
  abstract sendAudio(chatId:string, msg:MessageAudio, opt?:MessageSendOption):Promise<void>
  abstract sendFile(chatId:string, msg:MessageFile, opt?:MessageSendOption):Promise<void>
  abstract sendSticker(chatId:string, msg:MessageSticker, opt?:MessageSendOption):Promise<void>
  abstract sendWecomVoice(chatId:string, msg:MessageWecomVoice, opt?:MessageSendOption):Promise<void>
  abstract sendWecomCard(chatId:string, msg:MessageWecomCard, opt?:MessageSendOption):Promise<void>
  abstract sendWecomVideoChannel(chatId:string, msg:MessageWecomVideoChannel, opt?:MessageSendOption):Promise<void>
  abstract sendYCloudTemplate(chatId:string, msg:MessageYCloudTemplate, opt?:MessageSendOption):Promise<void>

  async sendMsg(chatId:string, msg:MessageAll[], opt?:MessageSendOption):Promise<void> {
    for (const singleMsg of msg) {
      if (singleMsg.type == SendMessageType.text) {
        await this.sendText(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.image) {
        await this.sendImage(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.video) {
        await this.sendVideo(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.audio) {
        await this.sendAudio(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.file) {
        await this.sendFile(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.sticker) {
        await this.sendSticker(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomVoice) {
        await this.sendWecomVoice(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomCard) {
        await this.sendWecomCard(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomVideoChannel) {
        await this.sendWecomVideoChannel(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.yCloudTemplate) {
        await this.sendYCloudTemplate(chatId, singleMsg, opt)
      }
      await commonSleep()
    }
  }
}

export interface HasRepeatedMsg {
  hasRepeatedMsg(chat_id: string, toMatch: string): Promise<boolean>
}