import { HasRepeatedMsg, MessageSender } from '.'
import { MessageText, MessageImage, MessageVideo, MessageAudio, MessageFile, MessageSticker, MessageWecomVoice, MessageWecomCard, MessageWecomVideoChannel, MessageSendOption, MessageYCloudTemplate } from './type'
import { WhatsappMessageMedia, WhatsappMessageTemplate, YCloudMessageSender } from '../../message_handler/ycloud/message_sender'
import logger from 'model/logger/logger'
import { WhatsappMessageTemplateComponent } from '@ycloud-cpaas/ycloud-sdk-node'

export class YCloudCommonMessageSender extends MessageSender {
  private ycloudMessageSender:YCloudMessageSender

  constructor(ycloudMessageSender:YCloudMessageSender, hasRepeatedMsg:HasRepeatedMsg) {
    super(hasRepeatedMsg)
    this.ycloudMessageSender = ycloudMessageSender
  }

  async sendText(chatId: string, msg: MessageText, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.text.trim())
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'text',
      ai_msg:msg.text
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendImage(chatId: string, msg: MessageImage, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'image',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        caption:'image'
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendVideo(chatId: string, msg: MessageVideo, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'video',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        caption:'video'
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendAudio(chatId: string, msg: MessageAudio, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'audio',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendFile(chatId: string, msg: MessageFile, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'document',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        filename:msg.filename
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendSticker(chatId: string, msg: MessageSticker, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'sticker',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendWecomVoice(chatId: string, msg: MessageWecomVoice, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomCard(chatId: string, msg: MessageWecomCard, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomVideoChannel(chatId: string, msg: MessageWecomVideoChannel, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }

  async sendYCloudTemplate(chatId: string, msg: MessageYCloudTemplate, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const components:WhatsappMessageTemplateComponent[] = [{
      type:'body',
      parameters:msg.variable.map((item) => ({
        type:'text',
        text:item
      }))
    }]
    if (msg.header?.type == 'image') {
      components.push({
        type:'header',
        parameters:[{
          type:'image',
          image:{
            link: msg.header.url
          }
        }]
      })
    } else if (msg.header?.type == 'document') {
      const url = msg.header.url
      const splitedUrl = url.split('/')
      const name = splitedUrl[splitedUrl.length - 1]
      components.push({
        type:'header',
        parameters:[{
          type:'document',
          document:{
            link: msg.header.url,
            filename:name
          }
        }]
      })
    } else if (msg.header?.type == 'video') {
      components.push({
        type:'header',
        parameters:[{
          type:'video',
          video:{
            link:msg.header.url
          }
        }]
      })
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'template',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageTemplate>{
        name:msg.templateName,
        language:{
          code:msg.language
        },
        components:components
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
}