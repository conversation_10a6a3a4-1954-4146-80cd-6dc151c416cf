import { Queue } from 'bullmq'
import { Config } from 'config'
import logger from 'model/logger/logger'
import { RedisDB } from 'model/redis/redis'
import { RedisCacheDB } from 'model/redis/redis_cache'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { ScheduleTask } from './schedule'
import {  ITask, Sop, SopScheduleTime, getVisualizedRedisSopKey } from './visualized_sop_type'
import { getBotId } from 'config/chat_id'

export async function startTasks(chatStateStoreClient:ChatStateStore, enterPriseName:string, userId: string, chatId: string, calTaskTime:(time: SopScheduleTime, chat_id: string)=> Promise<Date|undefined>, force:boolean = false) {
  // 如果已经添加过一次任务了，不再创建任务
  if (!force && (await chatStateStoreClient.get(chatId)).state.is_add_tasks) {
    return
  }

  const tasks = await VisualizedSopTasks.getTaskList(enterPriseName, userId, chatId, getBotId(chatId))
  // 使用Promise.all进行并发操作
  await Promise.all(
    tasks.map(async (task) => {
      task.sendTime = await calTaskTime(
        task.scheduleTime,
        task.chatId
      )
    })
  )

  await ScheduleTask.addTasks(getVisualizedSopQueueName(enterPriseName, getBotId(chatId)), tasks)

  await chatStateStoreClient.update(chatId, {
    state: {
      is_add_tasks: true,
    },
  })
}

export class VisualizedSopTasks {
  static async getTaskList(enterPriseName:string, userId: string, chatId: string, botId:string): Promise<ITask[]> {
    const sops = await this.getTasks(enterPriseName, botId)
    return sops.map((sop) => {
      return {
        name: sop.id,
        chatId,
        userId,
        scheduleTime: {
          day: sop.day,
          week: sop.week,
          time: sop.time,
        },
      }
    })
  }

  static async getTasks(enterPriseName:string, botId:string) {
    const redisClient = RedisDB.getInstance()
    const redisResult = await redisClient.get(getVisualizedRedisSopKey(enterPriseName, botId))
    if (!redisResult) {
      logger.error('redis查询失败')
      return []
    }
    return JSON.parse(redisResult) as Sop[]
  }


  static async clearSop(enterpriseName:string, chatId:string, botId = Config.setting.wechatConfig!.id) {
    const redisClient = RedisCacheDB.getInstance()
    const queue = new Queue<ITask>(
      getVisualizedSopQueueName(enterpriseName, botId),
      {
        connection: redisClient,
      }
    )
    const data = (await queue.getJobs()).filter((item) => item.data.chatId == chatId)
    for (const sop of data) {
      await sop.remove()
    }
  }
}

export function getVisualizedSopQueueName(enterpriseName:string, botId:string): string {
  return `${enterpriseName}-sop-${botId}`
}