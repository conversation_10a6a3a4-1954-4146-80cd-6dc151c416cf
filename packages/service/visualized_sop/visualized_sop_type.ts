import { IWecomMessage } from 'model/juzi/type'

export enum TextType {
  variable = 'variable',
  fixed = 'fixed',
}

export type ConditionType = 'fixed' | 'dynamic'

export interface TextFixed {
  type: TextType.fixed
  text: string
}

export interface TextVariable {
  type: TextType.variable
  tag: string
}

export interface Situation {
  conditions: Condition[]
  action: Action[]
}

export interface GroupSituation {
  conditions: GroupCondition[]
  action: GroupAction[]
}

export interface GroupCondition {
  isOrNotIs: boolean
  condition: string
}

export type GroupAction = ContentTextPlain | ContentImage | ContentCustom | ContentVideo | ContentFile | ContentVoice | ContentLink | ContentVideoChannel | ContentModifyGroupAnnouncement;

export interface Sop {
  id: string
  title: string
  week: number
  day:number
  time: string
  situations:Situation[]
  enable:boolean
  tag: string
  topic: string
}

export type Action = ContentTextPlain | ContentImage | ContentCustom | ContentVideo | ContentAudio | ContentFile | ContentVoice | ContentLink | ContentVideoChannel | ContentDynamicPrompt | ContentYCloudTemplate;

export interface ContentTextPlain {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.text
  textList: (TextFixed | TextVariable)[]
  description: string
}

export interface ContentImage {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.image
  url: string
  description: string
}

export interface ContentVideo {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.video
  url: string
  description: string
}

export interface ContentVoice {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.voice
  url: string
  duration: number
  description: string
}

export interface ContentLink {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.link
  description: string
  source: LinkSource
  title: string
  summary: string
  imageUrl: string
}

export type LinkSource = LinkSourceFixed | LinkSourceVariable

export interface LinkSourceFixed {
  type:LinkSourceType.fixed
  url: string
}
export interface LinkSourceVariable {
  type:LinkSourceType.variable
  tag: string
}

export enum LinkSourceType {
  fixed = 'fixed',
  variable = 'variable'
}

export interface ContentFile {
  [key: string]: any
  type: ActionType.file
  name: string
  url: string
  description: string
}

export interface ContentCustom {
  [key: string]: any
  type: ActionType.custom
  tag: string
}

export interface ContentDynamicPrompt {
  [key: string]: any
  type: ActionType.dynamicPrompt
  description: string
  customPrompt: string
  dynamicPrompt: string
  chatHistoryRounds: number
  noSplit:boolean

  includeRAG: boolean
  includeMemory: boolean
  includeUserSlots: boolean
  includeTimeInfo: boolean
  includeUserBehavior: boolean
}

export interface ContentAudio {
  [key: string]: any
  type: ActionType.audio
  description: string
  url:string
}

export interface ContentVideoChannel {
  [key: string]: any
  description:string
  type: ActionType.videoChannel
  avatarUrl: string      // 头像地址
  coverUrl: string       // 封面地址
  contentDescription: string    // 描述
  nickname: string       // 昵称
  thumbUrl: string       // 缩略图地址
  url: string            // 视频号地址
  extras: string         // 未知，请使用收到的字段信息
}

export interface ContentModifyGroupAnnouncement {
  [key: string]: any
  description: string
  type: ActionType.modifyGroupAnnouncement
  content: string
}

export interface ContentYCloudTemplate {
  [key: string]:any
  description:string,
  type:ActionType.ycloudTemplate
  templateName:string
  language: string
  header?: {
    type:'video' | 'document' | 'image'
    url: string
  }
  variable: string[]
}

export enum ActionType {
  text = 'text',
  link = 'link',
  dynamicPrompt = 'dynamic_prompt',
  image = 'image',
  video = 'video',
  voice = 'voice',
  file = 'file',
  custom = 'custom',
  audio = 'audio',
  videoChannel = 'video_channel',
  modifyGroupAnnouncement = 'modify_group_announcement',
  ycloudTemplate = 'yclout_template'
}

export type Condition = {
  //逻辑是正还是负
  isOrNotIs: boolean
  type: ConditionType
  condition: string
};

export function getVisualizedRedisSopKey (enterpriseName:string, botId:string) {
  return `${enterpriseName}:${botId}:visualized_sop`
}

export function getVisualizedRedisGroupSopKey(enterpriseName:string, groupId:string) {
  return `${enterpriseName}:${groupId}:visualized_group_sop`
}

export interface SopScheduleTime {
  day: number // 1-7 表示周一到周日
  week: number // 0表示上课周
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

export interface ISendMedia {
   description: string // 描述
   msg: IWecomMessage // 消息
}

export interface IScheduleTask {
    sendTime?: Date // 发送时间
    scheduleTime: SopScheduleTime
}

export interface ITask extends IScheduleTask, IBaseInfo{}
export interface IGroupTask extends IScheduleTask, IGroupBaseInfo{}

export interface IBaseInfo {
    name: string // 任务名
    chatId: string
    userId: string
    force?: boolean
}

export interface IGroupBaseInfo {
  name: string
  groupId: string
}

export interface GroupSop {
  id:string
  title:string
  week:number
  day :number
  time:string
  situations: GroupSituation[]
  enable: boolean
  tag:string
  topic:string
}

export interface SopTopic {
  id: string
  name:string
  enable:boolean
  tag:string
  conditions: TopicCondition[]
}

export interface TopicCondition {
  isOrNotIs: boolean
  condition: string
}

export function getSopTopicConditionRedisKey(enterpriseName:string, tag:string, topic:string) {
  return `${enterpriseName}:sop_topic:${tag}:${topic}`
}