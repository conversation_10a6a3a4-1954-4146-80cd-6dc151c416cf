import { GroupNotification } from '../group_notification/group_notification'

export abstract class BaseWorkFlow {
  /**
   * 对话流程
   * @param chat_id
   * @param user_id
   * @param userMessage
   */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    throw new Error('Not implemented')
  }

  public static async humanInvolveGroupNotify(contactName: string, courseNo: number | null, userMessage: string) {
    throw new Error('Not implemented')
  }


}