# 图片Captioning处理器

这个工具用于处理聊天记录中的图片，使用LLM生成图片描述并将其与原问题结合。

## 功能特点

- 🖼️ **自动图片识别**: 自动识别聊天记录中包含图片的条目（`media_url` 不为 null）
- 🤖 **LLM图片描述**: 使用封装好的LLM `imageChat` 方法生成详细的图片描述
- 📝 **智能文本合并**: 将图片描述以【图片描述】开头，与原问题智能合并
- 🔄 **批量处理**: 支持批量处理多条图片记录，自动添加延迟避免API限制
- 📊 **进度跟踪**: 实时显示处理进度和结果统计
- 🛡️ **错误处理**: 单条记录处理失败不影响其他记录的处理

## 使用方法

### 1. 基本用法

```bash
# 处理所有图片记录
NODE_ENV=dev ts-node scripts/image_captioning_processor.ts chat_data_part_01.json

# 指定输出文件
NODE_ENV=dev ts-node scripts/image_captioning_processor.ts chat_data_part_01.json output.json

# 限制处理数量（用于测试）
NODE_ENV=dev ts-node scripts/image_captioning_processor.ts chat_data_part_01.json output.json 3
```

### 2. 使用npm脚本

```bash
# 运行完整处理
npm run image-caption chat_data_part_01.json

# 运行测试
npm run test-image-caption
```

## 输入格式

输入的JSON文件应包含聊天记录数组，每条记录格式如下：

```json
{
  "user_id": "7881301020042397",
  "session_id": "S:1688854299473422_7881301020042397",
  "msg_type": 11041,
  "timestamp_sec": 1729045376,
  "time": "2024-10-16 10:22:56",
  "q": "老师我这里没有抄底先锋",
  "a": "",
  "type": "1",
  "media_url": "https://example.com/image.jpg"
}
```

## 输出格式

处理后的记录中，`q` 字段会被更新为：

```json
{
  "q": "【图片描述】这是一张手机界面截图，顶部显示时间为10:36，信号为5G，电量73%。界面标题为"抄底先锋"，下方展示一张活动海报... 老师我这里没有抄底先锋"
}
```

## 处理结果示例

### 处理前
```
q: "老师我这里没有抄底先锋"
media_url: "https://zhyt-wxwork-cdn.integrity.com.cn/download/File/20241016/4643bd6bdccb332f8bf6db09ce6338e2.jpg"
```

### 处理后
```
q: "【图片描述】这是一张手机界面截图，顶部显示时间为10:36，信号为5G，电量73%。界面标题为"抄底先锋"，下方展示一张活动海报。海报主色调为橙色，中央有"限时折扣""邀请您一起加入""抄底先锋 福利来袭 点击领取福利"等文字，并配有礼盒、气球等装饰图案。活动信息显示：抄底先锋，价格0.00元（原价3899.00元），活动时间为2024.05.23至2025.05.23。来源标注"中和应泰"，右下角有二维码用于了解详情。底部注明"创客匠人技术支持"。页面最下方有提示"长按上图保存活动海报发送好友"，并展示多种海报模板供选择。 老师我这里没有抄底先锋"
```

## 配置说明

### 环境变量
- `NODE_ENV=dev`: 使用开发环境配置

### LLM配置
- **模型**: 默认使用 `gpt-4.1`
- **温度**: 0（确保输出稳定）
- **最大token**: 300
- **延迟**: 每条记录处理后延迟2秒

### 图片描述提示词
```
请对这张图片进行详细描述。要求：
1. 描述要客观、准确、详细
2. 重点关注图片中的文字内容、界面元素、人物、物品等关键信息
3. 如果是截图，请描述界面的布局和主要功能
4. 描述长度控制在100-200字之间
5. 直接输出描述内容，不需要额外的格式
```

## 文件结构

```
scripts/
├── image_captioning_processor.ts    # 主处理器
├── test_image_captioning.ts         # 测试脚本
├── test_llm_connection.ts           # LLM连接测试
└── README_image_captioning.md       # 使用说明
```

## 注意事项

1. **API限制**: 处理大量图片时会有API调用限制，脚本已自动添加延迟
2. **网络连接**: 确保网络连接稳定，图片URL可访问
3. **文件备份**: 建议在处理前备份原始文件
4. **错误处理**: 单条记录失败不会影响整体处理，会在控制台显示错误信息

## 处理统计

处理完成后会显示详细统计信息：
- 总记录数
- 包含图片的记录数
- 成功处理的记录数
- 处理成功率

## 故障排除

### 常见问题

1. **LLM连接失败**
   ```bash
   # 先测试LLM连接
   NODE_ENV=dev ts-node scripts/test_llm_connection.ts
   ```

2. **图片URL无法访问**
   - 检查网络连接
   - 确认图片URL有效性

3. **内存不足**
   - 使用限制处理数量参数分批处理
   - 增加系统内存

## 开发者信息

- 基于现有的LLM封装类实现
- 支持多种LLM客户端（Azure OpenAI、OpenAI、Cheap OpenAI、Qwen）
- 自动错误重试和客户端切换
