import * as fs from 'fs'
import * as path from 'path'
import { LLM } from '../packages/lib/ai/llm/llm_model'

interface ChatRecord {
  user_id: string
  session_id: string
  msg_type: number
  timestamp_sec: number
  time: string
  q: string
  a: string
  type: string
  media_url: string | null
}

/**
 * 图片captioning处理器
 * 处理聊天记录中的图片，生成描述并更新question字段
 */
export class ImageCaptioningProcessor {
  private inputFilePath: string
  private outputFilePath: string

  constructor(inputFilePath: string, outputFilePath?: string) {
    this.inputFilePath = inputFilePath
    this.outputFilePath = outputFilePath || inputFilePath.replace('.json', '_with_captions.json')
  }

  /**
   * 处理聊天数据文件
   */
  async processFile(maxRecords?: number): Promise<void> {
    console.log(`开始处理文件: ${this.inputFilePath}`)

    // 读取JSON文件
    const chatData = this.readChatData()
    console.log(`总共读取到 ${chatData.length} 条记录`)

    // 找到包含图片的记录
    let imageRecords = chatData.filter(record => record.media_url !== null)
    console.log(`找到 ${imageRecords.length} 条包含图片的记录`)

    // 如果指定了最大处理数量，则限制处理的记录数
    if (maxRecords && maxRecords > 0) {
      imageRecords = imageRecords.slice(0, maxRecords)
      console.log(`限制处理前 ${maxRecords} 条图片记录`)
    }

    // 处理每条包含图片的记录
    let processedCount = 0
    for (const record of imageRecords) {
      try {
        console.log(`正在处理第 ${processedCount + 1}/${imageRecords.length} 条图片记录...`)
        console.log(`图片URL: ${record.media_url}`)
        console.log(`原始问题: "${record.q}"`)

        await this.processImageRecord(record)
        processedCount++
        console.log(`✅ 成功处理图片`)
        console.log(`更新后问题: "${record.q}"`)
        console.log('---')

        // 添加延迟避免API限制
        await this.delay(2000)
      } catch (error) {
        console.error(`❌ 处理图片失败: ${record.media_url}`, error)
        // 继续处理下一条记录
      }
    }

    // 保存更新后的数据
    this.saveChatData(chatData)
    console.log(`\n🎉 处理完成！成功处理 ${processedCount} 条图片记录`)
    console.log(`结果已保存到: ${this.outputFilePath}`)
  }

  /**
   * 读取聊天数据文件
   */
  private readChatData(): ChatRecord[] {
    try {
      const fileContent = fs.readFileSync(this.inputFilePath, 'utf-8')
      return JSON.parse(fileContent) as ChatRecord[]
    } catch (error) {
      throw new Error(`读取文件失败: ${error}`)
    }
  }

  /**
   * 保存聊天数据文件
   */
  private saveChatData(data: ChatRecord[]): void {
    try {
      const jsonContent = JSON.stringify(data, null, 2)
      fs.writeFileSync(this.outputFilePath, jsonContent, 'utf-8')
    } catch (error) {
      throw new Error(`保存文件失败: ${error}`)
    }
  }

  /**
   * 处理单条包含图片的记录
   */
  private async processImageRecord(record: ChatRecord): Promise<void> {
    if (!record.media_url) {
      return
    }

    try {
      // 使用LLM对图片进行captioning
      const caption = await this.generateImageCaption(record.media_url)

      // 更新question字段
      const originalQuestion = record.q || ''
      record.q = this.combineQuestionWithCaption(originalQuestion, caption)

    } catch (error) {
      console.error(`生成图片描述失败: ${record.media_url}`, error)
      throw error
    }
  }

  /**
   * 生成图片描述
   */
  private async generateImageCaption(imageUrl: string): Promise<string> {
    const llm = new LLM({
      temperature: 0,
      max_tokens: 300,
      meta: {
        promptName: 'image_caption_for_chat_data',
        description: '聊天数据图片描述生成'
      }
    })

    const prompt = `请对这张图片进行详细描述。要求：
1. 描述要客观、准确、详细
2. 重点关注图片中的文字内容、界面元素、人物、物品等关键信息
3. 如果是截图，请描述界面的布局和主要功能
4. 描述长度控制在100-200字之间
5. 直接输出描述内容，不需要额外的格式`

    const result = await llm.imageChat(imageUrl, prompt)
    return result || '无法生成图片描述'
  }

  /**
   * 将图片描述与原问题结合
   */
  private combineQuestionWithCaption(originalQuestion: string, caption: string): string {
    const formattedCaption = `【图片描述】${caption.trim()}`

    if (originalQuestion.trim()) {
      return `${formattedCaption} ${originalQuestion.trim()}`
    } else {
      return formattedCaption
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 主函数 - 处理命令行参数并执行处理
 */
async function main() {
  const args = process.argv.slice(2)

  if (args.length === 0) {
    console.log('使用方法: NODE_ENV=dev ts-node scripts/image_captioning_processor.ts <输入文件路径> [输出文件路径] [最大处理数量]')
    console.log('示例: NODE_ENV=dev ts-node scripts/image_captioning_processor.ts chat_data_part_01.json')
    console.log('示例: NODE_ENV=dev ts-node scripts/image_captioning_processor.ts chat_data_part_01.json output.json 3')
    process.exit(1)
  }

  const inputFile = args[0]
  const outputFile = args[1]
  const maxRecords = args[2] ? parseInt(args[2]) : undefined

  // 检查输入文件是否存在
  if (!fs.existsSync(inputFile)) {
    console.error(`错误: 输入文件不存在: ${inputFile}`)
    process.exit(1)
  }

  try {
    // 设置环境变量
    process.env.NODE_ENV = process.env.NODE_ENV || 'dev'

    const processor = new ImageCaptioningProcessor(inputFile, outputFile)
    await processor.processFile(maxRecords)
  } catch (error) {
    console.error('处理过程中发生错误:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本，则执行main函数
if (require.main === module) {
  main().catch(console.error)
}
