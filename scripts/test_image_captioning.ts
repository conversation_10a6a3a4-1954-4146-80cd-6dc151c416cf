import { ImageCaptioningProcessor } from './image_captioning_processor'
import * as fs from 'fs'

/**
 * 测试图片captioning处理器
 */
async function testImageCaptioning() {
  console.log('开始测试图片captioning处理器...')
  
  const inputFile = 'chat_data_part_01.json'
  const outputFile = 'chat_data_part_01_with_captions.json'
  
  // 检查输入文件是否存在
  if (!fs.existsSync(inputFile)) {
    console.error(`错误: 输入文件不存在: ${inputFile}`)
    return
  }
  
  try {
    // 先读取原始数据，查看包含图片的记录数量
    const originalData = JSON.parse(fs.readFileSync(inputFile, 'utf-8'))
    const imageRecords = originalData.filter((record: any) => record.media_url !== null)
    
    console.log(`原始数据包含 ${originalData.length} 条记录`)
    console.log(`其中包含图片的记录有 ${imageRecords.length} 条`)
    
    // 显示前几条包含图片的记录
    console.log('\n前3条包含图片的记录:')
    imageRecords.slice(0, 3).forEach((record: any, index: number) => {
      console.log(`${index + 1}. 问题: "${record.q}"`)
      console.log(`   图片URL: ${record.media_url}`)
      console.log(`   时间: ${record.time}`)
      console.log('---')
    })
    
    // 创建处理器并执行处理
    const processor = new ImageCaptioningProcessor(inputFile, outputFile)
    await processor.processFile()
    
    // 验证处理结果
    if (fs.existsSync(outputFile)) {
      const processedData = JSON.parse(fs.readFileSync(outputFile, 'utf-8'))
      const processedImageRecords = processedData.filter((record: any) => 
        record.media_url !== null && record.q.includes('【图片描述】')
      )
      
      console.log(`\n处理结果验证:`)
      console.log(`处理后数据包含 ${processedData.length} 条记录`)
      console.log(`成功添加图片描述的记录有 ${processedImageRecords.length} 条`)
      
      // 显示处理后的前几条记录
      console.log('\n处理后的前3条包含图片描述的记录:')
      processedImageRecords.slice(0, 3).forEach((record: any, index: number) => {
        console.log(`${index + 1}. 更新后的问题: "${record.q}"`)
        console.log(`   图片URL: ${record.media_url}`)
        console.log(`   时间: ${record.time}`)
        console.log('---')
      })
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
}

// 执行测试
if (require.main === module) {
  testImageCaptioning().catch(console.error)
}

export { testImageCaptioning }
