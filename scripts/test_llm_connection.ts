import { LLM } from '../packages/lib/ai/llm/llm_model'

/**
 * 测试LLM连接和图片处理功能
 */
async function testLLMConnection() {
  console.log('开始测试LLM连接...')
  
  try {
    // 测试基本文本生成
    console.log('1. 测试基本文本生成...')
    const llm = new LLM({
      temperature: 0,
      max_tokens: 100,
      meta: { 
        promptName: 'test_connection', 
        description: '测试连接' 
      }
    })
    
    const textResponse = await llm.predict('请简单回复"连接成功"')
    console.log('文本生成结果:', textResponse)
    
    // 测试图片处理功能
    console.log('\n2. 测试图片处理功能...')
    const testImageUrl = 'https://zhyt-wxwork-cdn.integrity.com.cn/download/File/20241016/4643bd6bdccb332f8bf6db09ce6338e2.jpg'
    
    const imageResponse = await llm.imageChat(testImageUrl, '请简单描述这张图片的内容，不超过50字。')
    console.log('图片描述结果:', imageResponse)
    
    console.log('\n✅ LLM连接测试成功！')
    
  } catch (error) {
    console.error('❌ LLM连接测试失败:', error)
    throw error
  }
}

// 执行测试
if (require.main === module) {
  // 设置环境变量
  process.env.NODE_ENV = 'dev'
  
  testLLMConnection().catch(console.error)
}

export { testLLMConnection }
